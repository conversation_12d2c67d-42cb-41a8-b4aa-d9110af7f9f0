'use client';

import { countTestResults } from '@/lib/test-utils';
import { DeviceData, TestResults, TestStats } from '@/lib/types';
import { useEffect, useState } from 'react';

export function useTestStats(
  completionTrigger: boolean,
  devices: DeviceData[],
  testResults: TestResults,
  setCompletionTrigger: (value: boolean) => void
) {
  // Test statistics state
  const [testStats, setTestStats] = useState<TestStats>({
    duration: 0,
    panelsTested: 0,
    pcbsTested: 0,
    successPcbs: 0,
    failurePcbs: 0,
  });

  // Start a timer for test duration
  useEffect(() => {
    const timer = setInterval(() => {
      setTestStats(prev => ({
        ...prev,
        duration: prev.duration + 1,
      }));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Reset timer when a new test starts (when PCB status changes)
  useEffect(() => {
    // Check if a test reset has occurred - this happens when PCB status message is received
    // We detect this by checking for the 'not_started' status or by checking if the global reset flag is set
    const testResetOccurred = devices.some(device => device.testStatus === 'not_started');

    if (testResetOccurred) {
      console.log('Test reset detected, resetting elapsed time');
      setTestStats(prev => ({
        ...prev,
        duration: 0,
        // Keep all cumulative counts including success/failure counts
      }));
    }
  }, [devices]);

  // Handle test completion and update stats
  useEffect(() => {
    // Skip initial render
    if (!completionTrigger) return;

    console.log('Previous test stats:', testStats);

    // Get only the number of active devices from testResults length
    const activeDevicesCount = Object.keys(testResults).length;

    // Log all devices and their test status for debugging
    console.log('All devices test status:');
    devices.forEach(device => {
      console.log(
        `Device ${device.id}: testStatus=${device.testStatus}, hasResults=${Boolean(
          testResults[device.id]
        )}, hasState3=${device.hasState3Occurred}`
      );
    });

    // Use our utility function to count successes and failures
    const { successPcbs, failurePcbs } = countTestResults(devices, testResults);

    // Log detailed information about each device's state and success criteria
    devices.forEach(device => {
      if (testResults[device.id]) {
        const result = testResults[device.id];
        const chargingSuccess = result.chargingSuccess === true;
        const dischargingSuccess = result.dischargingSuccess === true;
        const combinedSuccess = result.chargingAndDischargingSuccess === true;

        const isSuccess = device.hasState3Occurred
          ? chargingSuccess && dischargingSuccess && combinedSuccess
          : chargingSuccess && dischargingSuccess;

        // Create a detailed reason message
        let reason = 'All tests passed';
        let failureDetails = [];

        if (!isSuccess) {
          if (!chargingSuccess) {
            failureDetails.push('Charging failed');
          }

          if (!dischargingSuccess) {
            failureDetails.push('Discharging failed');
          }

          if (device.hasState3Occurred && !combinedSuccess) {
            failureDetails.push('Combined charging/discharging failed');
          }

          reason = failureDetails.join(', ');
        }

        console.log(`Device ${device.id} (${device.name}):`, {
          hasState3Occurred: device.hasState3Occurred,
          chargingSuccess,
          dischargingSuccess,
          chargingAndDischargingSuccess: combinedSuccess,
          isSuccess,
          reason,
          testStatus: device.testStatus,
        });
      }
    });

    console.log('Test Results:', testResults);
    console.log('Active Count:', activeDevicesCount);
    console.log('Final counts from testResults:', { successPcbs, failurePcbs });

    // Log detailed information about each device's state and success criteria
    devices.forEach(device => {
      if (testResults[device.id]) {
        const result = testResults[device.id];
        const chargingSuccess = result.chargingSuccess === true;
        const dischargingSuccess = result.dischargingSuccess === true;
        const combinedSuccess = result.chargingAndDischargingSuccess === true;

        const isSuccess = device.hasState3Occurred
          ? chargingSuccess && dischargingSuccess && combinedSuccess
          : chargingSuccess && dischargingSuccess;

        // Create a detailed reason message
        let reason = 'All tests passed';
        let failureDetails = [];

        if (!isSuccess) {
          if (!chargingSuccess) {
            failureDetails.push('Charging failed');
          }

          if (!dischargingSuccess) {
            failureDetails.push('Discharging failed');
          }

          if (device.hasState3Occurred && !combinedSuccess) {
            failureDetails.push('Combined charging/discharging failed');
          }

          reason = failureDetails.join(', ');
        }

        console.log(`Device ${device.id} (${device.name}):`, {
          hasState3Occurred: device.hasState3Occurred,
          chargingSuccess,
          dischargingSuccess,
          chargingAndDischargingSuccess: combinedSuccess,
          isSuccess,
          reason,
          testStatus: device.testStatus,
          rawResults: result, // Log the raw results for debugging
          rawCombinedValue: result.chargingAndDischargingSuccess,
        });
      }
    });

    // Update test statistics
    setTestStats(prev => ({
      ...prev,
      pcbsTested: prev.pcbsTested + activeDevicesCount,
      successPcbs: prev.successPcbs + successPcbs,
      failurePcbs: prev.failurePcbs + failurePcbs,
      panelsTested: prev.panelsTested + (activeDevicesCount > 0 ? 1 : 0),
    }));

    setCompletionTrigger(false); // Reset trigger after processing
  }, [completionTrigger, devices, testResults, setCompletionTrigger]);

  return testStats;
}
