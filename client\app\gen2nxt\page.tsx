"use client"

import { useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Grid3X3, CircuitBoard } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function Gen2NxtPage() {
  const router = useRouter()

  useEffect(() => {
    // Auto-redirect to /gen2nxt/panel after 2 seconds
    const timer = setTimeout(() => {
      router.push("/gen2nxt/panel")
    }, 2000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <div className="p-6">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600">
          Gen2Nxt Dashboard
        </h1>
        <p className="text-slate-600">Advanced battery testing and monitoring system</p>
      </div>

      <div className="grid grid-cols-1 gap-6 max-w-xl mx-auto">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50 hover:shadow-xl transition-all">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-blue-100 rounded-full">
                <Grid3X3 className="h-5 w-5 text-blue-600" />
              </div>
              Panel Dashboard
            </CardTitle>
            <CardDescription>Monitor multiple devices in panel view with detailed metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              asChild
              className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
            >
              <Link href="/gen2nxt/panel">View Panel Dashboard</Link>
            </Button>
          </CardContent>
        </Card>

        <div className="flex justify-center items-center gap-4 mt-4">
          <CircuitBoard className="h-5 w-5 text-slate-400" />
          <p className="text-center text-sm text-slate-500">Redirecting to Panel Dashboard in 2 seconds...</p>
        </div>
      </div>
    </div>
  )
}
