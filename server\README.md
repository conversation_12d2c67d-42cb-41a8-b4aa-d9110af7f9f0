# Qualifuse MQTT Monitoring Service

This Node.js service monitors MQTT messages from the Qualifuse testing system and generates CSV reports when tests complete.

## Features

- Connects to the MQTT broker at the configured URL
- Monitors test data from the `pico/testing` topic
- Tracks device data during tests
- Tracks which devices have PCBs based on the PCB status message
- Monitors battery levels from the PCB status message
- Generates CSV reports when tests complete (state 4)
- Appends test results to a single daily report file with the format `YYYY-MM-DD_12V3A2C.csv`
- Assigns the same serial number to all devices in the same panel/test
- Calculates and maintains test statistics (panels tested, PCBs tested, success/failure counts)
- Provides API endpoints to access test statistics and report data
- Streams real-time test data to clients using Server-Sent Events (SSE)
- Sends test statistics, PCB status, battery levels, and device data during tests
- Sends final test statistics after test completion

## Installation

1. Make sure you have Node.js installed (v14 or later recommended)
2. Install dependencies:

```bash
npm install
```

## Configuration

Configure the MQTT connection details in the `.env` file:

```env
MQTT_URL=ws://192.168.0.144:9001/
MQTT_USERNAME=resonate
MQTT_PASSWORD=Resonate@123
MQTT_TOPIC=pico/testing
```

## Usage

Start the monitoring service:

```bash
npm start
```

The service will connect to the MQTT broker, subscribe to the configured topic, and start monitoring test data. When a test completes (state 4), a CSV report will be generated in the `reports` directory.

## CSV Report Format

The CSV report includes the following columns:

- Device ID
- Serial Number
- Charging Status (boolean or "NA" if state didn't occur)
- Discharging Status (boolean or "NA" if state didn't occur)
- Charging And Discharging Status (boolean or "NA" if state didn't occur)
- Charging Voltage (V)
- Charging Current (A)
- Discharging Voltage (V)
- Discharging Current (A)
- Battery Level (%)

## API Endpoints

The service provides the following API endpoints:

### Get Test Statistics

```http
GET /api/test-stats
```

Returns the test statistics from today's report only:

```json
{
  "panelsTested": 1,
  "pcbsTested": 3,
  "successPcbs": 2,
  "failurePcbs": 1
}
```

If no report exists for today, returns zeros:

```json
{
  "panelsTested": 0,
  "pcbsTested": 0,
  "successPcbs": 0,
  "failurePcbs": 0
}
```

### Real-time Test Data (SSE)

```http
GET /api/sse/test-data
```

This endpoint uses Server-Sent Events (SSE) to stream real-time test data to clients. The data includes:

- During tests:

  - Test statistics
  - PCB status array (which devices have PCBs)
  - Battery levels
  - Device data with current measurements
  - Raw channel data from MQTT

- After test completion:
  - Final test statistics
  - Test results (success/failure counts)

Example client usage:

```javascript
const eventSource = new EventSource('/api/sse/test-data');

eventSource.onmessage = event => {
  const data = JSON.parse(event.data);
  console.log('Received test data:', data);

  // Process the data based on its content
  if (data.testInProgress) {
    // Update UI with current test data
    updateDeviceStatus(data.devices);
    updateBatteryLevels(data.batteryLevels);
    updatePcbStatus(data.pcb_status);
  } else if (data.testCompleted) {
    // Test is complete, update final statistics
    updateTestStats(data.testStats);
    showTestResults(data.finalReport);
  }
};

eventSource.onerror = error => {
  console.error('SSE error:', error);
  eventSource.close();
};
```

### Get Report by Date (JSON format)

```http
GET /api/reports/:date
```

Returns the data from a report for a specific date (format: YYYY-MM-DD) in JSON format:

```json
{
  "date": "2023-05-15",
  "filename": "2023-05-15_12V3A2C.csv",
  "headers": ["Device ID", "Serial Number", "Charging Status", ...],
  "data": [
    {
      "Device ID": "1",
      "Serial Number": "SN-671168",
      "Charging Status": "true",
      ...
    },
    ...
  ]
}
```

### Download Today's Report (CSV file)

```http
GET /api/reports/today/download
```

Downloads today's report as a CSV file. The file will be named in the format `YYYY-MM-DD_12V3A2C.csv`.

### Download Report by Date (CSV file)

```http
GET /api/reports/:date/download
```

Downloads a report for a specific date (format: YYYY-MM-DD) as a CSV file. The file will be named in the format `YYYY-MM-DD_12V3A2C.csv`.

## Example SSE Client

The service includes a simple example client that demonstrates how to use the SSE endpoint to receive real-time test data. You can access it at:

```http
http://localhost:3001/sse-client-example.html
```

This example shows how to:

- Connect to the SSE endpoint
- Process real-time test data
- Display device status, battery levels, and test statistics
- Handle test completion events

## Troubleshooting

If you encounter connection issues:

1. Verify that the MQTT broker is running and accessible
2. Check the MQTT connection details in the `.env` file
3. Check the console output for error messages
