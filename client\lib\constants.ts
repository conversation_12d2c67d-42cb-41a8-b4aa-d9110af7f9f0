// Status types
export const STATUS = {
  OK: 'ok' as const,
  WARNING: 'warning' as const,
  ERROR: 'error' as const,
};

export type StatusType = (typeof STATUS)[keyof typeof STATUS];

// Test status types
export const TEST_STATUS = {
  NOT_STARTED: 'not_started' as const,
  IN_PROGRESS: 'in_progress' as const,
  COMPLETED: 'completed' as const,
  FAILED: 'failed' as const,
  NO_PCB: 'no_pcb' as const,
};

export type TestStatusType = (typeof TEST_STATUS)[keyof typeof TEST_STATUS];

// Device states
export const DEVICE_STATE = {
  CHARGING: 1,
  DISCHARGING: 2,
  CHARGING_AND_DISCHARGING: 3,
  COMPLETED: 4,
};

// Activity and style types
export const ACTIVITY_TYPE = {
  CHARGING: 'charging' as const,
  DISCHARGING: 'discharging' as const,
  COMBINED: 'combined' as const,
  INACTIVE: 'inactive' as const,
};

export type ActivityType = (typeof ACTIVITY_TYPE)[keyof typeof ACTIVITY_TYPE];

// Style types (for backward compatibility)
export const STYLE_TYPE = {
  CHARGING: ACTIVITY_TYPE.CHARGING,
  DISCHARGING: ACTIVITY_TYPE.DISCHARGING,
  COMBINED: ACTIVITY_TYPE.COMBINED,
};

export type StyleType = (typeof STYLE_TYPE)[keyof typeof STYLE_TYPE];

// Activity colors
export const ACTIVITY_COLORS = {
  [ACTIVITY_TYPE.CHARGING]: {
    text: 'text-green-700',
    bg: 'bg-green-500',
    icon: 'text-green-500',
    gradient: 'from-green-50 to-green-100',
    border: 'border-green-200',
  },
  [ACTIVITY_TYPE.DISCHARGING]: {
    text: 'text-orange-700',
    bg: 'bg-orange-500',
    icon: 'text-orange-500',
    gradient: 'from-orange-50 to-orange-100',
    border: 'border-orange-200',
  },
  [ACTIVITY_TYPE.COMBINED]: {
    text: 'text-teal-700',
    bg: 'bg-teal-500',
    icon: 'text-teal-500',
    gradient: 'from-teal-50 to-teal-100',
    border: 'border-teal-200',
  },
  [ACTIVITY_TYPE.INACTIVE]: {
    text: 'text-muted-foreground',
    bg: 'bg-slate-200',
    icon: 'text-muted-foreground',
    gradient: 'from-white to-slate-50',
    border: 'border-slate-200',
  },
};
