'use client';

// Import shared utility functions
import {
  getBatteryIconInfo,
  getCardBorderColor,
  getComponentStyle,
  getDeviceActivityType,
  getProgressBarColor,
  getStatusIconInfo,
  getTextColor,
  hasDeviceFailed,
  deviceToBatteryData,
  getBatteryActivityType,
  getActivityColor,
} from './device-utils';

// Import constants
import { STATUS, TEST_STATUS, ACTIVITY_TYPE, ActivityType } from './constants';
import {
  BatteryCharging,
  BatteryFull,
  BatteryLow,
  BatteryMedium,
  BatteryWarning,
  Battery,
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
} from 'lucide-react';

// Create React component for battery indicator
export function getBatteryIndicator(
  batteryLevel: number,
  isCharging: boolean,
  isChargingAndDischarging: boolean = false,
  size: 'sm' | 'md' | 'lg' | 'card' = 'lg',
  hasPCB: boolean = true
) {
  const iconInfo = getBatteryIconInfo(
    batteryLevel,
    isCharging,
    isChargingAndDischarging,
    size,
    hasPCB
  );

  // Return the appropriate icon component based on the type
  switch (iconInfo.type) {
    case 'refresh-cw':
      return <RefreshCw className={iconInfo.className} />;
    case 'battery-charging':
      return <BatteryCharging className={iconInfo.className} />;
    case 'alert-circle':
      return <AlertCircle className={iconInfo.className} />;
    case 'battery-full':
      return <BatteryFull className={iconInfo.className} />;
    case 'battery-medium':
      return <BatteryMedium className={iconInfo.className} />;
    case 'battery-low':
      return <BatteryLow className={iconInfo.className} />;
    case 'battery-warning':
      return <BatteryWarning className={iconInfo.className} />;
    default:
      return <Battery className={iconInfo.className} />;
  }
}

// Create React component for status icon
export function getStatusIcon(status: (typeof STATUS)[keyof typeof STATUS]) {
  const iconInfo = getStatusIconInfo(status);

  // Return the appropriate icon component based on the type
  switch (iconInfo.type) {
    case 'check-circle':
      return <CheckCircle className={iconInfo.className} />;
    case 'alert-triangle':
      return <AlertTriangle className={iconInfo.className} />;
    case 'alert-circle':
      return <AlertCircle className={iconInfo.className} />;
    default:
      return <AlertCircle className={iconInfo.className} />;
  }
}

// Re-export utility functions for backward compatibility
export {
  getCardBorderColor,
  hasDeviceFailed,
  getComponentStyle as getCardStyle,
  getComponentStyle as getStatusCardStyle,
  getTextColor,
  getDeviceActivityType,
  getProgressBarColor,
  deviceToBatteryData,
  getBatteryActivityType,
  getActivityColor,
  ACTIVITY_TYPE,
};

// Re-export types
export type { ActivityType };

// Format time in minutes to a readable format (MM:SS)
export function formatTime(seconds: number): string {
  if (!seconds || seconds <= 0) return '00:00';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Format duration in a concise way
export function formatDuration(seconds: number, concise = false): string {
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  if (minutes < 60 || concise) {
    const remainingSeconds = seconds % 60;
    return concise
      ? `${minutes}m${remainingSeconds > 0 ? ` ${remainingSeconds}s` : ''}`
      : `${minutes}m ${remainingSeconds}s`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  return concise
    ? `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`
    : `${hours}h ${remainingMinutes}m`;
}

// Create initial device data
export function createInitialDevice(id: number) {
  return {
    id,
    name: id.toString(),
    isCharging: false,
    isDischarging: false,
    isChargingAndDischarging: false,
    hasState3Occurred: false,
    batteryLevel: 100,
    progress: 100,
    timeRemaining: 0,
    chargingVoltage: 0,
    chargingCurrent: 0,
    dischargingVoltage: 0,
    dischargingCurrent: 0,
    chargingStatus: STATUS.WARNING,
    dischargingStatus: STATUS.WARNING,
    chargingAndDischargingStatus: STATUS.WARNING,
    chargingStatusMessage: 'Waiting for data',
    dischargingStatusMessage: 'Waiting for data',
    chargingAndDischargingStatusMessage: 'Waiting for data',
    testStatus: TEST_STATUS.NOT_STARTED,
  };
}
