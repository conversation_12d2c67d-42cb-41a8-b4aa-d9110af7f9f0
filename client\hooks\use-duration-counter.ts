'use client';

import { useEffect, useRef, useState } from 'react';

/**
 * Custom hook to automatically increment duration counter
 * @param initialDuration Initial duration in seconds
 * @param isTestInProgress Whether a test is currently in progress
 * @returns Current duration in seconds
 */
export function useDurationCounter(
  initialDuration: number = 0,
  isTestInProgress: boolean = false
): [number, (newDuration: number) => void] {
  // State to track the current duration
  const [duration, setDuration] = useState<number>(initialDuration);
  
  // Ref to track the last time the duration was updated
  const lastUpdateTimeRef = useRef<number>(Date.now());
  
  // Ref to track whether the counter is running
  const isRunningRef = useRef<boolean>(false);
  
  // Function to update the duration from external sources (like SSE)
  const updateDuration = (newDuration: number) => {
    setDuration(newDuration);
    lastUpdateTimeRef.current = Date.now();
  };

  // Effect to start/stop the counter based on test status
  useEffect(() => {
    isRunningRef.current = isTestInProgress;
    
    // If test just started, update the last update time
    if (isTestInProgress) {
      lastUpdateTimeRef.current = Date.now();
    }
  }, [isTestInProgress]);

  // Effect to increment the duration counter
  useEffect(() => {
    // Don't start the counter if no initial duration has been set yet
    if (duration === 0 && initialDuration === 0) {
      return;
    }
    
    const intervalId = setInterval(() => {
      if (isRunningRef.current) {
        setDuration(prevDuration => prevDuration + 1);
      }
    }, 1000);

    return () => clearInterval(intervalId);
  }, [initialDuration]);

  return [duration, updateDuration];
}
