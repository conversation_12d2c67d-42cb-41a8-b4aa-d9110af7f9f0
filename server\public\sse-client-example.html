<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Qualifuse Test Monitor - SSE Client Example</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1, h2, h3 {
      color: #333;
    }
    .container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
    }
    .stat-item {
      background-color: #f0f7ff;
      padding: 10px;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
    }
    .stat-label {
      font-size: 14px;
      color: #666;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #0066cc;
    }
    .device-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
    }
    .device-card {
      background-color: #f9f9f9;
      border-radius: 4px;
      padding: 10px;
      border-left: 4px solid #ccc;
    }
    .device-card.has-pcb {
      border-left-color: #4caf50;
    }
    .device-card.no-pcb {
      border-left-color: #f44336;
      opacity: 0.6;
    }
    .device-name {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .device-status {
      font-size: 14px;
      color: #666;
    }
    .battery-level {
      height: 10px;
      background-color: #e0e0e0;
      border-radius: 5px;
      margin-top: 5px;
    }
    .battery-fill {
      height: 100%;
      background-color: #4caf50;
      border-radius: 5px;
    }
    .connection-status {
      padding: 10px;
      margin-bottom: 20px;
      border-radius: 4px;
      font-weight: bold;
    }
    .connected {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    .disconnected {
      background-color: #ffebee;
      color: #c62828;
    }
    .log-container {
      background-color: #263238;
      color: #eeffff;
      padding: 15px;
      border-radius: 4px;
      font-family: monospace;
      height: 200px;
      overflow-y: auto;
    }
    .log-entry {
      margin-bottom: 5px;
      border-bottom: 1px solid #37474f;
      padding-bottom: 5px;
    }
    .log-time {
      color: #82aaff;
      margin-right: 10px;
    }
    .controls {
      margin-bottom: 20px;
    }
    button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    button:hover {
      background-color: #0055aa;
    }
    button.disconnect {
      background-color: #f44336;
    }
    button.disconnect:hover {
      background-color: #d32f2f;
    }
  </style>
</head>
<body>
  <h1>Qualifuse Test Monitor</h1>
  <p>This example demonstrates how to use the SSE endpoint to receive real-time test data.</p>
  
  <div class="controls">
    <button id="connect-btn">Connect to SSE</button>
    <button id="disconnect-btn" class="disconnect" disabled>Disconnect</button>
  </div>
  
  <div id="connection-status" class="connection-status disconnected">
    Disconnected
  </div>
  
  <div class="container">
    <div class="card">
      <h2>Test Statistics</h2>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">Panels Tested</span>
          <span id="panels-tested" class="stat-value">0</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">PCBs Tested</span>
          <span id="pcbs-tested" class="stat-value">0</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Success PCBs</span>
          <span id="success-pcbs" class="stat-value">0</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Failure PCBs</span>
          <span id="failure-pcbs" class="stat-value">0</span>
        </div>
      </div>
      
      <h3>Current State: <span id="current-state">Not Started</span></h3>
      <h3>Test Status: <span id="test-status">Not Running</span></h3>
    </div>
    
    <div class="card">
      <h2>Devices</h2>
      <div id="device-grid" class="device-grid">
        <!-- Device cards will be added here dynamically -->
      </div>
    </div>
  </div>
  
  <div class="card">
    <h2>Event Log</h2>
    <div id="log-container" class="log-container">
      <!-- Log entries will be added here dynamically -->
    </div>
  </div>
  
  <script>
    // DOM elements
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const connectionStatus = document.getElementById('connection-status');
    const panelsTested = document.getElementById('panels-tested');
    const pcbsTested = document.getElementById('pcbs-tested');
    const successPcbs = document.getElementById('success-pcbs');
    const failurePcbs = document.getElementById('failure-pcbs');
    const currentState = document.getElementById('current-state');
    const testStatus = document.getElementById('test-status');
    const deviceGrid = document.getElementById('device-grid');
    const logContainer = document.getElementById('log-container');
    
    // SSE connection
    let eventSource = null;
    
    // Connect to SSE endpoint
    function connectToSSE() {
      // Close existing connection if any
      if (eventSource) {
        eventSource.close();
      }
      
      // Create new EventSource
      eventSource = new EventSource('/api/sse/test-data');
      
      // Set up event handlers
      eventSource.onopen = () => {
        updateConnectionStatus(true);
        logEvent('Connected to SSE endpoint');
      };
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        logEvent(`Received data: ${JSON.stringify(data).substring(0, 100)}...`);
        
        // Process the data
        processSSEData(data);
      };
      
      eventSource.onerror = (error) => {
        logEvent(`SSE error: ${error.type}`);
        updateConnectionStatus(false);
      };
      
      // Update UI
      connectBtn.disabled = true;
      disconnectBtn.disabled = false;
    }
    
    // Disconnect from SSE endpoint
    function disconnectFromSSE() {
      if (eventSource) {
        eventSource.close();
        eventSource = null;
        
        // Update UI
        updateConnectionStatus(false);
        logEvent('Disconnected from SSE endpoint');
        
        connectBtn.disabled = false;
        disconnectBtn.disabled = true;
      }
    }
    
    // Update connection status UI
    function updateConnectionStatus(isConnected) {
      connectionStatus.textContent = isConnected ? 'Connected' : 'Disconnected';
      connectionStatus.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
    }
    
    // Process SSE data
    function processSSEData(data) {
      // Update test statistics if available
      if (data.testStats) {
        panelsTested.textContent = data.testStats.panelsTested || 0;
        pcbsTested.textContent = data.testStats.pcbsTested || 0;
        successPcbs.textContent = data.testStats.successPcbs || 0;
        failurePcbs.textContent = data.testStats.failurePcbs || 0;
      }
      
      // Update test status
      if (data.testInProgress !== undefined) {
        testStatus.textContent = data.testInProgress ? 'Running' : 'Not Running';
      }
      
      // Update current state
      if (data.currentState !== undefined) {
        let stateName = 'Unknown';
        switch (data.currentState) {
          case 1: stateName = 'Charging'; break;
          case 2: stateName = 'Discharging'; break;
          case 3: stateName = 'Charging & Discharging'; break;
          case 4: stateName = 'Completed'; break;
          default: stateName = 'Not Started';
        }
        currentState.textContent = stateName;
      }
      
      // Update device grid if PCB status and devices are available
      if (data.pcb_status && data.batteryLevels && data.devices) {
        updateDeviceGrid(data.pcb_status, data.batteryLevels, data.devices);
      }
      
      // Handle test completion
      if (data.testCompleted) {
        logEvent(`Test completed! Success: ${data.finalReport?.successCount || 0}, Failure: ${data.finalReport?.failureCount || 0}`);
      }
    }
    
    // Update device grid
    function updateDeviceGrid(pcbStatus, batteryLevels, devices) {
      // Clear existing device cards
      deviceGrid.innerHTML = '';
      
      // Create a card for each possible device (9 devices)
      for (let i = 0; i < 9; i++) {
        const deviceId = i + 1;
        const hasPCB = pcbStatus[i];
        const batteryLevel = batteryLevels[i] || 0;
        
        // Find device data if available
        const deviceData = devices.find(d => d.deviceID === deviceId);
        
        // Create device card
        const deviceCard = document.createElement('div');
        deviceCard.className = `device-card ${hasPCB ? 'has-pcb' : 'no-pcb'}`;
        
        // Device name
        const deviceName = document.createElement('div');
        deviceName.className = 'device-name';
        deviceName.textContent = `Device ${deviceId}`;
        deviceCard.appendChild(deviceName);
        
        // PCB status
        const pcbStatusElem = document.createElement('div');
        pcbStatusElem.className = 'device-status';
        pcbStatusElem.textContent = hasPCB ? 'PCB: Present' : 'PCB: Not Present';
        deviceCard.appendChild(pcbStatusElem);
        
        // Battery level
        const batteryLevelContainer = document.createElement('div');
        batteryLevelContainer.className = 'device-status';
        batteryLevelContainer.textContent = `Battery: ${batteryLevel}%`;
        deviceCard.appendChild(batteryLevelContainer);
        
        const batteryBar = document.createElement('div');
        batteryBar.className = 'battery-level';
        const batteryFill = document.createElement('div');
        batteryFill.className = 'battery-fill';
        batteryFill.style.width = `${batteryLevel}%`;
        batteryBar.appendChild(batteryFill);
        deviceCard.appendChild(batteryBar);
        
        // Add device data if available
        if (deviceData && hasPCB) {
          // Charging status
          if (deviceData.chargingStatus !== undefined) {
            const chargingStatus = document.createElement('div');
            chargingStatus.className = 'device-status';
            chargingStatus.textContent = `Charging: ${deviceData.chargingStatus === 'NA' ? 'N/A' : deviceData.chargingStatus}`;
            deviceCard.appendChild(chargingStatus);
          }
          
          // Discharging status
          if (deviceData.dischargingStatus !== undefined) {
            const dischargingStatus = document.createElement('div');
            dischargingStatus.className = 'device-status';
            dischargingStatus.textContent = `Discharging: ${deviceData.dischargingStatus === 'NA' ? 'N/A' : deviceData.dischargingStatus}`;
            deviceCard.appendChild(dischargingStatus);
          }
          
          // Charging & Discharging status
          if (deviceData.chargingAndDischargingStatus !== undefined) {
            const combinedStatus = document.createElement('div');
            combinedStatus.className = 'device-status';
            combinedStatus.textContent = `Combined: ${deviceData.chargingAndDischargingStatus === 'NA' ? 'N/A' : deviceData.chargingAndDischargingStatus}`;
            deviceCard.appendChild(combinedStatus);
          }
        }
        
        // Add to grid
        deviceGrid.appendChild(deviceCard);
      }
    }
    
    // Add log entry
    function logEvent(message) {
      const logEntry = document.createElement('div');
      logEntry.className = 'log-entry';
      
      const time = new Date().toLocaleTimeString();
      const logTime = document.createElement('span');
      logTime.className = 'log-time';
      logTime.textContent = time;
      
      logEntry.appendChild(logTime);
      logEntry.appendChild(document.createTextNode(message));
      
      logContainer.appendChild(logEntry);
      logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // Event listeners
    connectBtn.addEventListener('click', connectToSSE);
    disconnectBtn.addEventListener('click', disconnectFromSSE);
    
    // Log initial message
    logEvent('Page loaded. Click "Connect to SSE" to start receiving test data.');
  </script>
</body>
</html>
