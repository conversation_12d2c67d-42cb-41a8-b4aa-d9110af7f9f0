'use client';

import { useState, useEffect } from 'react';
import { DeviceData } from '@/lib/types';
import { useDeviceData } from './use-device-data';

/**
 * Custom hook to get a specific device by ID with live updates
 * @param deviceId The ID or name of the device to get
 * @returns The device data with live updates
 */
export function useDeviceById(deviceId: string | null) {
  const { devices } = useDeviceData();
  const [device, setDevice] = useState<DeviceData | null>(null);

  useEffect(() => {
    if (!deviceId) {
      setDevice(null);
      return;
    }

    // Find the device by ID or name
    const foundDevice = 
      devices.find(d => d.id.toString() === deviceId || d.name === deviceId) ||
      // Fallback to index-based lookup (legacy support)
      devices[parseInt(deviceId) - 1] ||
      null;
    
    setDevice(foundDevice);
  }, [deviceId, devices]); // Re-run when devices array changes (from MQTT updates)

  return device;
}
