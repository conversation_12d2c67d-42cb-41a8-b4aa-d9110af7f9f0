/* Progress bar styles */
/* Charging progress bar (green gradient) */
.progress-bar-charging {
  background-image: linear-gradient(to right, rgb(134, 239, 172), rgb(22, 163, 74));
}

/* Charging error progress bar (red gradient) */
.progress-bar-charging-error {
  background-image: linear-gradient(to right, rgb(252, 165, 165), rgb(220, 38, 38));
}

/* Discharging progress bar (orange gradient) */
.progress-bar-discharging {
  background-image: linear-gradient(to right, rgb(251, 146, 60), rgb(234, 88, 12));
}

/* Discharging error progress bar (red gradient) */
.progress-bar-discharging-error {
  background-image: linear-gradient(to right, rgb(252, 165, 165), rgb(220, 38, 38));
}

/* Combined charging and discharging progress bar (teal-orange gradient) */
.progress-bar-combined {
  background-image: linear-gradient(
    to right,
    rgb(134, 239, 172),
    rgb(45, 212, 191),
    rgb(251, 146, 60)
  );
}

/* Combined error progress bar (red gradient) */
.progress-bar-combined-error {
  background-image: linear-gradient(to right, rgb(252, 165, 165), rgb(220, 38, 38));
}

/* Progress bar widths in 5% increments */
.progress-width-0 {
  width: 0%;
}
.progress-width-5 {
  width: 5%;
}
.progress-width-10 {
  width: 10%;
}
.progress-width-15 {
  width: 15%;
}
.progress-width-20 {
  width: 20%;
}
.progress-width-25 {
  width: 25%;
}
.progress-width-30 {
  width: 30%;
}
.progress-width-35 {
  width: 35%;
}
.progress-width-40 {
  width: 40%;
}
.progress-width-45 {
  width: 45%;
}
.progress-width-50 {
  width: 50%;
}
.progress-width-55 {
  width: 55%;
}
.progress-width-60 {
  width: 60%;
}
.progress-width-65 {
  width: 65%;
}
.progress-width-70 {
  width: 70%;
}
.progress-width-75 {
  width: 75%;
}
.progress-width-80 {
  width: 80%;
}
.progress-width-85 {
  width: 85%;
}
.progress-width-90 {
  width: 90%;
}
.progress-width-95 {
  width: 95%;
}
.progress-width-100 {
  width: 100%;
}
