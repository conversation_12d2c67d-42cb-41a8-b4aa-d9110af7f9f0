import { ProductVariants } from './types';

// Product variants data
export const productVariants: ProductVariants = {
  Standard: {
    name: 'Standard Battery',
    description: 'Standard battery pack for general use',
    specs: {
      voltage: '3.7V',
      capacity: '2000mAh',
      dimensions: '65mm x 45mm x 8mm',
    },
  },
  Extended: {
    name: 'Extended Battery',
    description: 'High-capacity battery for extended operation',
    specs: {
      voltage: '3.7V',
      capacity: '3500mAh',
      dimensions: '65mm x 45mm x 10mm',
    },
  },
  Compact: {
    name: 'Compact Battery',
    description: 'Slim profile battery for space-constrained applications',
    specs: {
      voltage: '3.7V',
      capacity: '1500mAh',
      dimensions: '60mm x 40mm x 6mm',
    },
  },
};
