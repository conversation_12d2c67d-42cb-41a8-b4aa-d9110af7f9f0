

# Import required modules
import utime

import ubinascii

from micropython import const

# Define constants for better readability
SHUNT_VOLTAGE_LSB_MV = const(0.04)  # 40uV = 40/1000 mV
BUS_VOLTAGE_LSB = const(8)               # Bus voltage LSB = 8mV
I2C_READ_DELAY_MS = const(100)           # Delay between I2C reads

# Global variable with clear name
VERBOSE_LOGGING = False

# Channel type constants for better readability
CHANNEL_TYPE_CHARGING = const(0)
CHANNEL_TYPE_BATTERY = const(1)
CHANNEL_TYPE_DISCHARGING = const(2)



# Function to determine the shunt voltage with improved error handling
#@micropython.viper
def compute_shunt_voltage(value):
    """
    Compute the shunt voltage from raw bytes data.
    
    Args:
        value (bytes): Raw bytes data from I2C read
        
    Returns:
        float: Computed shunt voltage in mV
        
    Raises:
        ValueError: If the data cannot be properly converted
    """
    try:
        # Convert data to int
        value = int.from_bytes(value, 'big')

        # Convert the number from 2's complement to actual value
        if value & 0x8000:  # Negative Number
            value = -(value ^ 0xFFFF - 1)

        # Bits 2:0 are reserved
        value = value >> 3

        # Shunt voltage calculation using the constant
        value = value * SHUNT_VOLTAGE_LSB_MV

        # Return the shunt Voltage
        return value
        
    except Exception as e:
        print(f"Error in compute_shunt_voltage: {e}")
        return 0.0

# Function to determine the bus voltage with improved error handling
def compute_bus_voltage(value):
    """
    Compute the bus voltage from raw bytes data.
    
    Args:
        value (bytes): Raw bytes data from I2C read
        
    Returns:
        float: Computed bus voltage in mV
        
    Raises:
        ValueError: If the data cannot be properly converted
    """
    try:
        # Convert data to int
        value = int.from_bytes(value, 'big')

        # Convert the number from 2's complement to actual value
        if value & 0x8000:  # Negative Number
            value = -(value ^ 0xFFFF - 1)

        # Bits 2:0 are reserved
        value = value >> 3

        # Bus voltage calculation using the constant
        value = value * BUS_VOLTAGE_LSB

        # Return the bus voltage
        return value
        
    except Exception as e:
        print(f"Error in compute_bus_voltage: {e}")
        return 0.0

# Function to turn on or off the input and battery channels with improved error handling
def config(channel, i2c, activate=True):
    """
    Configure (activate or deactivate) a specific channel.
    
    Args:
        channel (int): Channel number (1-3)
        i2c (machine.I2C): I2C interface object
        activate (bool): True to activate, False to deactivate
        
    Returns:
        bool: True if successful, False otherwise
    """
    
    
    CHANNEL_BITS = {
        1: 0b1110000000000000,
        2: 0b0001110000000000,
        3: 0b0000001110000000
    }
        
    
    if channel not in CHANNEL_BITS:
        print(f"Error: Invalid channel number {channel}")
        return False
        
    try:
        # Get the mask according to the channels
        mask = CHANNEL_BITS.get(channel)
        
        # Get the address of the relay - with error handling
        scan_result = i2c.scan()
        if len(scan_result) < 4:
            print(f"Error: Expected at least 4 I2C devices, found {len(scan_result)}")
            return False
          
        # First device is relay.    
        relay_address = scan_result[0]
        
        
        # Reading the lsb from the port0
        try:
            current_state_port0 = i2c.readfrom_mem(relay_address, 0x02, 1)[0]
            print(f"Current state port0: 0x{current_state_port0:02x}")
        except Exception as e:
            print(f"Error reading from port0: {e}")
            return False
            
        # Read the msb from the port1
        try:
            current_state_port1 = i2c.readfrom_mem(relay_address, 0x03, 1)[0]
            print(f"Current state port1: 0x{current_state_port1:02x}")
            
        except Exception as e:
            print(f"Error reading from port1: {e}")
            return False
        
        # If channel is activated then
        if activate:
            # Set the bits for the selected channel
            new_state_port0 = current_state_port0 | (mask & 0xFF)        # Setting the lower 8bits
            new_state_port1 = current_state_port1 | ((mask >> 8) & 0xFF) # Setting the upper 8bits
            
        else:
            # Clear the bits for the selected channel
            new_state_port0 = current_state_port0 & ~(mask & 0xFF)         # Clear the lower 8 bits
            new_state_port1 = current_state_port1 & ~((mask >> 8) & 0xFF)  # Clear the upper 8 bits
        
               #print(f"New state port0 binary: {bin(new_state_port0)[2:].zfill(8)}")
        print(f"New state port0: 0x{new_state_port0:02x}")
        
        #print(f"New state port1 binary: {bin(new_state_port1)[2:].zfill(8)}")
        print(f"New state port1: 0x{new_state_port1:02x}")
        
        
        # Write the new states to both ports with error handling
        try:
            i2c.writeto_mem(relay_address, 0x02, bytes([new_state_port0]))
            utime.sleep_ms(I2C_READ_DELAY_MS)
            
            i2c.writeto_mem(relay_address, 0x03, bytes([new_state_port1]))
            utime.sleep_ms(I2C_READ_DELAY_MS)
           
            return True
        
        except Exception as e:
            print(f"Error writing to ports: {e}")
            return False
            
    except Exception as e:
        print(f"Error in config: {e}")
        return False

# Function to monitor the i2c channels with improved error handling
#@micropython.native
def monitor(channel, i2c):
    """
    Monitor a specific channel to get voltage and current readings.
    
    Args:
        channel (int): Channel number (1-3)
        i2c (machine.I2C): I2C interface object
        
    Returns:
        list: List of dictionaries containing voltage and current readings for each subchannel
    """
    # Variable to hold the shunt resistor value
    shunt_resistor = 10  # 10 mOhm.
    
    # Initialize data structure for monitoring results
    charging_channel = [{"V": 0.0, "C": 0.0}, {"V": 0.0, "C": 0.0}, {"V": 0.0, "C": 0.0}]
    
    try:
        # Get the device address with error handling
        scan_result = i2c.scan()
        if channel <= 0 or channel > len(scan_result):
            print(f"Error: Invalid channel {channel} for available devices {len(scan_result)}")
            return charging_channel
        
        if len(scan_result) < 4:
            print(f"Error: Expected at least 4 I2C devices, found {len(scan_result)}")
            return charging_channel
        
        #[32, 64, 65, 66]    
        # The address which I am getting from the scan result is [64, 65, 66]
        # Address 64: 1N1-IN3, 66- IN4-IN6, 65 - IN7-IN9. Hence need to swap for list.
        temp = scan_result[2]
        scan_result[2] = scan_result[3]
        scan_result[3] = temp
        
        
        device_address = scan_result[channel]
        
        # Read the shunt voltage and bus voltage for all the channels
        for channel_index in range(3):
            
            try:
                # Read the shunt voltage register for the channel
                data = i2c.readfrom_mem(device_address, channel_index * 2 + 1, 2)
                
                # Compute the shunt voltage
                shunt_voltage = compute_shunt_voltage(data)
                
                # Display the shunt voltage if verbose mode is on
                if VERBOSE_LOGGING:
                    print('INA3221 Channel {:d} Shunt Voltage (0x{:02d}) Read: 0x{:s} ({:02.03f}mV)'.format(
                        channel_index + 1, channel_index * 2 + 1, ubinascii.hexlify(data), shunt_voltage))
                
                # Determine the current based on 'ohms law'
                current = shunt_voltage / shunt_resistor
                current = round(current, 3)
                
                # Wait for finite time before next read 
                utime.sleep_ms(10)
                
                # Read the bus voltage register for the channel
                data = i2c.readfrom_mem(device_address, channel_index * 2 + 2, 2)
                
                # Compute the bus voltage
                bus_voltage = compute_bus_voltage(data)
                
                # Display the bus voltage if verbose mode is on
                if VERBOSE_LOGGING:
                    print('INA3221 Channel {:d} Bus Voltage (0x{:02d}) Read: 0x{:s} ({:02.03f}mV)'.format(
                        channel_index + 1, channel_index * 2 + 2, ubinascii.hexlify(data), bus_voltage))
                
                bus_voltage = round(bus_voltage / 1000, 2)
                
                
                # Update the charging channel
                charging_channel[channel_index]["V"] = bus_voltage  # Convert to volts
                charging_channel[channel_index]["C"] = current      
                          
                
            except Exception as e:
                print(f"Error reading channel {channel_index}: {e}")
                # Keep default values for this channel
                
            # Wait for finite time before next read
            utime.sleep_ms(10)
            
        return charging_channel
        
    except Exception as e:
        print(f"Error in monitor: {e}")
        return charging_channel


# Update the channel data with improved error checking and consistent naming
def update(channel_type, channel_data, display_channel_data):
    
    """
    Update the global display data with new channel readings.
    
    Args:
        channel (int): Channel number (1-3)
        channel_type (str): Type of channel ('charging', 'battery', etc.)
        channel_data (list): New channel data to update
        display_channel_data (dict): Current display data to update
        
    Returns:
        dict: Updated display channel data
    """
    try:
        # Check if the required keys exist in display_channel_data, if not create them
        if channel_type not in display_channel_data:
            display_channel_data[channel_type] = {"V": [0, 0, 0], "C": [0, 0, 0]}
            
        # Check if the channel_data has the expected length
        if len(channel_data) != 3:
            print(f"Error: Expected 3 channels in data, got {len(channel_data)}")
            return display_channel_data
            
        # Update the three channels with consistent naming (using "C" for current)
        for channel_index in range(len(channel_data)):
            # Safely get voltage (V)
            v_value = channel_data[channel_index].get("V", 0.0)
            display_channel_data[channel_type]["V"][channel_index] = v_value
            
            # Safely get current (C) but store as "C" in display data for consistency
            c_value = channel_data[channel_index].get("C", 0.0)
            display_channel_data[channel_type]["C"][channel_index] = c_value
        
        return display_channel_data
        
    except Exception as e:
        print(f"Error in update: {e}")
        return display_channel_data


# Function to set verbose mode
def set_verbose(verbose=False):
    """
    Set the verbose logging mode.
    
    Args:
        verbose (bool): True to enable verbose logging, False to disable
    """
    global VERBOSE_LOGGING
    VERBOSE_LOGGING = verbose