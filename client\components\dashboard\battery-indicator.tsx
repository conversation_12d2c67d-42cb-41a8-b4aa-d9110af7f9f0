'use client';

import { BatteryFull, Zap, RefreshCw, AlertCircle } from 'lucide-react';
import { getBatteryActivityType, getActivityColor, ACTIVITY_TYPE } from '@/lib/dashboard-utils';

interface BatteryIndicatorProps {
  batteryLevel: number;
  isCharging: boolean;
  isChargingAndDischarging?: boolean;
  hasPCB?: boolean;
}

export function BatteryIndicator({
  batteryLevel,
  isCharging,
  isChargingAndDischarging = false,
  hasPCB = true,
}: BatteryIndicatorProps) {
  // Determine the activity type
  const activityType = getBatteryActivityType({
    chargingStatus: isCharging,
    dischargingStatus: !isCharging && !isChargingAndDischarging, // If not charging or combined, then discharging
    chargingAndDischargingStatus: isChargingAndDischarging,
    batteryLevel,
  });

  // Get the appropriate background color
  const bgColorClass = getActivityColor(activityType, 'bg');
  const iconColorClass = getActivityColor(activityType, 'icon');

  return (
    <div className="battery-indicator">
      <BatteryFull className="h-16 w-16 text-muted-foreground" />
      <div
        className={`battery-indicator-fill ${bgColorClass}`}
        style={{ height: `${batteryLevel}%` }}
      />
      {!hasPCB ? (
        <AlertCircle className="h-6 w-6 text-red-600 battery-indicator-icon" />
      ) : isChargingAndDischarging ? (
        <RefreshCw className={`h-6 w-6 ${iconColorClass} battery-indicator-icon`} />
      ) : (
        isCharging && <Zap className={`h-6 w-6 ${iconColorClass} battery-indicator-icon`} />
      )}
    </div>
  );
}
