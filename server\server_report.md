# MQTT Monitoring Service Implementation Report

## Overview

This report documents the implementation of a Node.js MQTT monitoring service for the Qualifuse testing system. The service connects to the MQTT broker, monitors test data, and generates CSV reports when tests complete.

## Implementation Details

### Files Created

1. **mqtt-monitor.js**: The main MQTT monitoring service that connects to the broker, processes messages, and generates reports.
2. **test-mqtt.js**: A test script that simulates MQTT messages for testing the monitoring service.
3. **package.json**: Configuration file for Node.js dependencies and scripts.
4. **.env**: Environment variables for MQTT connection details.
5. **.gitignore**: Git configuration to exclude node_modules and generated reports.
6. **README.md**: Documentation for the MQTT monitoring service.

### Features Implemented

1. **MQTT Connection**: The service connects to the MQTT broker at *************:9001 using the same credentials as the client code.
2. **Message Processing**: The service processes different types of MQTT messages:
   - PCB status messages
   - State messages (charging, discharging, charging and discharging, completed)
   - Channel data messages
3. **Device Data Tracking**: The service tracks device data during tests, including:
   - Charging status
   - Discharging status
   - Charging and discharging status
   - Charging voltage and current
   - Discharging voltage and current
   - Battery level
4. **Report Generation**: When a test completes (state 4), the service generates a CSV report with the format `YYYY-MM-DD_12V3A2C.csv`.
5. **Test Simulation**: A test script is provided to simulate MQTT messages for testing the service.

### CSV Report Format

The CSV report includes the following columns:

- Device ID
- Serial Number
- Charging Status (boolean)
- Discharging Status (boolean)
- Charging And Discharging Status (boolean)
- Charging Voltage (V)
- Charging Current (A)
- Discharging Voltage (V)
- Discharging Current (A)
- Battery Level (%)
- Timestamp

## Usage Instructions

1. Install dependencies:

   ```bash
   npm install
   ```

2. Start the monitoring service:

   ```bash
   npm start
   ```

3. For development with auto-restart:

   ```bash
   npm run dev
   ```

4. To test with simulated MQTT messages:

   ```bash
   npm test
   ```

## Future Improvements

1. Add more robust error handling and reconnection logic
2. Implement data validation and filtering
3. Add support for different device types and test configurations
4. Create a web interface for viewing and managing reports
5. Add authentication and security features
