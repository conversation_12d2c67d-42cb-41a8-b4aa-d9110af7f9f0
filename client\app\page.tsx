"use client"

import { useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON>R<PERSON>, Battery } from "lucide-react"

import { Button } from "@/components/ui/button"

export default function Home() {
  const router = useRouter()

  useEffect(() => {
    // Auto-redirect to /gen2nxt after 2 seconds
    const timer = setTimeout(() => {
      router.push("/gen2nxt")
    }, 2000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <main className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      {/* Hero background with pattern */}
      <div className="absolute inset-0 bg-grid-slate-200 [mask-image:linear-gradient(to_bottom,transparent,white)] bg-[length:20px_20px] opacity-25"></div>

      <div className="relative z-10 text-center max-w-3xl mx-auto bg-white/70 backdrop-blur-sm p-8 rounded-xl shadow-lg border border-slate-200">
        <div className="mb-6 flex justify-center">
          <div className="p-3 bg-blue-100 rounded-full">
            <Battery className="h-12 w-12 text-blue-600" />
          </div>
        </div>
        <h1 className="text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600">
          Battery Testing Dashboard
        </h1>
        <p className="text-xl text-slate-600 mb-8">Advanced monitoring and analytics for battery performance metrics</p>
        <Button
          asChild
          size="lg"
          className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
        >
          <Link href="/gen2nxt">
            Go to Dashboard <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
        <p className="mt-4 text-sm text-slate-500">Redirecting in 2 seconds...</p>
      </div>
    </main>
  )
}
