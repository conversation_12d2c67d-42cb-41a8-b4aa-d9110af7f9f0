

# Import required modules
import utime

from micropython import const


# Define constants for better readability
MUX_ENABLE = const(1)        # MUX enable value
MUX_DISABLE = const(0)       # MUX disable value
UART_DELAY_MS = const(250)   # Standard delay for UART operations
UART_STABILIZE_MS = const(1) # Delay for UART stabilization
MAX_RETRY_COUNT = const(10)  # Maximum retry count for UART operations
DATA_TIMEOUT_MS = const(250) # Timeout for waiting for data

# Global variable for verbose logging
VERBOSE_LOGGING = False

# MUX control helper functions to reduce code duplication
def disable_mux(control_mux):
    """
    Disable a MUX by setting its enable pin to inactive state.
    
    Args:
        control_mux (list): List containing [enable_pin, select_pin_A0, select_pin_A1]
    """
    control_mux[0].value(MUX_DISABLE)
    
def configure_mux(control_mux, channel_index):
    """
    Configure a MUX to select a specific channel.
    
    Args:
        control_mux (list): List containing [enable_pin, select_pin_A0, select_pin_A1]
        channel_index (int): Channel index to select (0-3)
    """
    control_mux[1].value(channel_index & 0x1)
    control_mux[2].value((channel_index >> 1) & 0x1)
    
def enable_mux(control_mux):
    """
    Enable a MUX by setting its enable pin to active state.
    
    Args:
        control_mux (list): List containing [enable_pin, select_pin_A0, select_pin_A1]
    """
    control_mux[0].value(MUX_ENABLE)

def flush_uart_buffer(uart):
    """
    Flush any pending data in the UART buffer.
    
    Args:
        uart (machine.UART): UART interface object
    """
    if uart.any() > 0:
        uart.read()  # Read and discard data

# Function to configure the electronic load with improved error handling
def config(channel, electronic_load, control_mux_list, load_current):
    """
    Configure the electronic load for a specific channel and current.
    
    Args:
        channel (int): Channel number (1-3)
        electronic_load (machine.UART): UART interface for the electronic load
        control_mux_list (list): List of MUX control pin lists
        load_current (float): Current setting for the load in Amps
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Validate channel
        if channel < 1 or channel > len(control_mux_list):
            print(f"Error: Invalid channel {channel}")
            return False
            
        # Select the control mux
        control_mux = control_mux_list[channel - 1]
        
        # We are having the three channels to monitor in the single mux
        for channel_index in range(3): 
            try:
                # Disable the MUX
                disable_mux(control_mux)
                
                # Configure the MUX to select the required UART
                configure_mux(control_mux, channel_index)
                
                # Enable the MUX
                enable_mux(control_mux)
                
                # Flush any pending data
                flush_uart_buffer(electronic_load)
                
                # Wait for stabilization
                utime.sleep_ms(UART_STABILIZE_MS)
                
                # Send stop command
                if VERBOSE_LOGGING:
                    print('Sending \'stop\' command ...')
                electronic_load.write('stop'.encode('utf-8'))
                utime.sleep_ms(UART_DELAY_MS)
                
                # Check response
                data = electronic_load.read()
                if VERBOSE_LOGGING:
                    response = "None" if data is None else data.strip()
                    print(f"Response {response} ...")
                
                # Send start command
                if VERBOSE_LOGGING:
                    print('Sending \'start\' command ...')
                electronic_load.write('start'.encode('utf-8'))
                utime.sleep_ms(UART_DELAY_MS)
                
                # Check response
                data = electronic_load.read()
                if VERBOSE_LOGGING:
                    response = "None" if data is None else data.strip()
                    print(f"Response {response} ...")
                
                # Turn OFF the load
                if VERBOSE_LOGGING:
                    print('Sending \'off\' command ...')
                electronic_load.write('off'.encode('utf-8'))
                utime.sleep_ms(UART_DELAY_MS)
                
                # Set the load current
                if VERBOSE_LOGGING:
                    print(f'Setting load \'{load_current:.2f}A\' ...')
                electronic_load.write(f'{load_current:.2f}A'.encode('utf-8'))
                utime.sleep_ms(UART_DELAY_MS)
                
                # Turn ON the load if current is greater than zero
                if load_current > 0:
                    if VERBOSE_LOGGING:
                        print('Sending \'on\' command ...')
                    electronic_load.write('on'.encode('utf-8'))
                    utime.sleep_ms(UART_DELAY_MS)
                
            except Exception as e:
                print(f"Error configuring channel {channel_index}: {e}")
                return False
                
        return True
        
    except Exception as e:
        print(f"Error in config: {e}")
        return False

# Function to monitor the discharging channels with improved error handling
#@micropython.native
def monitor(channel, electronic_load, control_mux_list):
    """
    Monitor a specific channel to get voltage and current readings.
    
    Args:
        channel (int): Channel number (1-3)
        electronic_load (machine.UART): UART interface for the electronic load
        control_mux_list (list): List of MUX control pin lists
        
    Returns:
        list: List of dictionaries containing voltage and current readings for each subchannel
    """
    try:
        # Validate channel
        if channel < 1 or channel > len(control_mux_list):
            print(f"Error: Invalid channel discharging {channel}")
            return [{"V": 0.0, "C": 0.0}, {"V": 0.0, "C": 0.0}, {"V": 0.0, "C": 0.0}]
            
        # Select the control mux
        control_mux = control_mux_list[channel - 1]
        
        # List to hold all the discharging channel data
        discharging_channels = [{"V": 0.0, "C": 0.0}, {"V": 0.0, "C": 0.0}, {"V": 0.0, "C": 0.0}]
        
        # Initialize the channel index
        for channel_index in range(3):
            try:
                # Disable the MUX
                disable_mux(control_mux)
                
                # Configure the MUX to select the required UART
                configure_mux(control_mux, channel_index)
                
                # Enable the MUX
                enable_mux(control_mux)
                
                # Flush any pending data
                flush_uart_buffer(electronic_load)
                
                # Wait for stabilization
                utime.sleep_ms(UART_STABILIZE_MS)
                
                # Read data with timeout
                retry_count = 0
                data = bytes()
                
                while retry_count < MAX_RETRY_COUNT:
                    
                    # Check whether data is available
                    if electronic_load.any() > 0:
                        # Read the data
                        new_data = electronic_load.read()
                        if new_data:
                            data += new_data
                            
                        # Try to decode and check for complete line
                        try:
                            line = data.decode('utf-8')                            
                            
                            if 'fail' not in line.strip() and '\n' in line:
                                break
                            
                            data = bytes()
                            
                        except UnicodeError:
                            # Decoding error, discard corrupt data
                            data = bytes()
                    
                    # No data, increment retry counter
                    retry_count += 1
                    utime.sleep_ms(DATA_TIMEOUT_MS)
                
                # Disable the MUX after reading
                disable_mux(control_mux)
                
                # Process the data
                if data:
                    try:
                        line = data.decode('utf-8')
                        if '\n' in line:
                            line = line[:line.find('\n')]
                            
                            # Validate data format
                            if line.strip() != 'fail' and len(line.strip()) == 26:
                                # Parse the data
                                parts = line.strip().split(',')
                                if len(parts) == 4 and parts[0][-1] == 'V' and parts[1][-1] == 'A':
                                    try:
                                        discharging_channels[channel_index]["V"] = float(parts[0][:-1])
                                        discharging_channels[channel_index]["C"] = float(parts[1][:-1])
                                    except ValueError:
                                        # Handle value conversion errors
                                        pass
                        
                    except UnicodeError:
                        # Handle decoding errors
                        pass
            
            except Exception as e:
                print(f"Error monitoring channel {channel_index}: {e}")
                # Keep defaults for this channel
                
        return discharging_channels
        
    except Exception as e:
        print(f"Error in monitor: {e}")
        return [{"V": 0.0, "C": 0.0}, {"V": 0.0, "C": 0.0}, {"V": 0.0, "C": 0.0}]

# Update the global sensor data with improved error handling
def update(channel_name, channel_data, display_channel_data):
    """
    Update the global sensor data with new channel readings.
    
    Args:
        channel (int): Channel number (1-3)
        channel_name (str): Name of the channel in global data
        channel_data (list): New channel data to update
        global_sensor_data (dict): Global sensor data to update
        
    Returns:
        dict: Updated global sensor data
    """
    try:
        # Check if channel_name exists in global_sensor_data
        if channel_name not in display_channel_data:
            display_channel_data[channel_name] = {"V": [0, 0, 0], "C": [0, 0, 0]} 
            
        # Check if the channel_data has the expected length
        if len(channel_data) != 3:
            print(f"Error: Expected 3 channels in data, got {len(channel_data)}")
            return display_channel_data
             
        # Update the three channels with consistent naming (using "C" for current)
        for channel_index in range(len(channel_data)):
            # Safely get voltage (V)
            v_value = channel_data[channel_index].get("V", 0.0)
            display_channel_data[channel_name]["V"][channel_index] = v_value
            
            # Safely get current (C) but store as "C" in display data for consistency
            c_value = channel_data[channel_index].get("C", 0.0)
            display_channel_data[channel_name]["C"][channel_index] = c_value
        
        return display_channel_data
        
    except Exception as e:
        print(f"Error in update: {e}")
        return display_channel_data

# Function to set verbose mode
def set_verbose(verbose=False):
    """
    Set the verbose logging mode.
    
    Args:
        verbose (bool): True to enable verbose logging, False to disable
    """
    global VERBOSE_LOGGING
    VERBOSE_LOGGING = verbose