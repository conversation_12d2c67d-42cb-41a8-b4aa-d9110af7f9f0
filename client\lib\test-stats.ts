'use client';

import { TestStats } from './types';

/**
 * Implementation of TestStats that synchronizes successCount/failureCount with successPcbs/failurePcbs
 */
export class TestStatsImpl implements TestStats {
  duration: number;
  panelsTested: number;
  pcbsTested: number;
  
  private _successCount: number;
  private _failureCount: number;

  constructor(
    duration: number = 0,
    panelsTested: number = 0,
    pcbsTested: number = 0,
    successCount: number = 0,
    failureCount: number = 0
  ) {
    this.duration = duration;
    this.panelsTested = panelsTested;
    this.pcbsTested = pcbsTested;
    this._successCount = successCount;
    this._failureCount = failureCount;
  }

  // Getters and setters for successCount
  get successCount(): number {
    return this._successCount;
  }

  set successCount(value: number) {
    this._successCount = value;
  }

  // Getters and setters for failureCount
  get failureCount(): number {
    return this._failureCount;
  }

  set failureCount(value: number) {
    this._failureCount = value;
  }

  // Backward compatibility getters and setters
  get successPcbs(): number {
    return this._successCount;
  }

  set successPcbs(value: number) {
    this._successCount = value;
  }

  get failurePcbs(): number {
    return this._failureCount;
  }

  set failurePcbs(value: number) {
    this._failureCount = value;
  }

  /**
   * Create a TestStatsImpl from a plain object
   */
  static fromObject(obj: Partial<TestStats>): TestStatsImpl {
    const stats = new TestStatsImpl(
      obj.duration || 0,
      obj.panelsTested || 0,
      obj.pcbsTested || 0,
      obj.successCount || obj.successPcbs || 0,
      obj.failureCount || obj.failurePcbs || 0
    );
    return stats;
  }

  /**
   * Convert to a plain object
   */
  toObject(): TestStats {
    return {
      duration: this.duration,
      panelsTested: this.panelsTested,
      pcbsTested: this.pcbsTested,
      successCount: this._successCount,
      failureCount: this._failureCount,
      successPcbs: this._successCount,
      failurePcbs: this._failureCount,
    };
  }

  /**
   * Update stats with new values
   */
  update(updates: Partial<TestStats>): TestStatsImpl {
    if (updates.duration !== undefined) {
      this.duration = updates.duration;
    }
    
    if (updates.panelsTested !== undefined) {
      this.panelsTested = updates.panelsTested;
    }
    
    if (updates.pcbsTested !== undefined) {
      this.pcbsTested = updates.pcbsTested;
    }
    
    if (updates.successCount !== undefined) {
      this._successCount = updates.successCount;
    } else if (updates.successPcbs !== undefined) {
      this._successCount = updates.successPcbs;
    }
    
    if (updates.failureCount !== undefined) {
      this._failureCount = updates.failureCount;
    } else if (updates.failurePcbs !== undefined) {
      this._failureCount = updates.failurePcbs;
    }
    
    return this;
  }
}
