'use client';

/**
 * Updates progress bars in the UI based on data-progress attributes
 * This function should be called when the component mounts and when data changes
 */
export function updateProgressBars() {
  if (typeof window === 'undefined') return;
  
  // Use requestAnimationFrame for better performance
  window.requestAnimationFrame(() => {
    const progressBars = document.querySelectorAll('.progress-bar[data-progress]');
    progressBars.forEach(bar => {
      const progress = bar.getAttribute('data-progress');
      if (progress) {
        bar.style.width = `${progress}%`;
      }
    });
  });
}

/**
 * Sets up a MutationObserver to watch for changes to progress bars
 * Call this function in your component's useEffect hook
 */
export function setupProgressBarObserver() {
  if (typeof window === 'undefined') return () => {};
  
  // Initial update
  updateProgressBars();
  
  // Set up a MutationObserver to watch for changes
  const observer = new MutationObserver(updateProgressBars);
  observer.observe(document.body, { 
    childList: true, 
    subtree: true,
    attributes: true,
    attributeFilter: ['data-progress']
  });
  
  // Return a cleanup function
  return () => {
    observer.disconnect();
  };
}
