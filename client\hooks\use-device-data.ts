'use client';

import { DEVICE_STATE, STATUS, TEST_STATUS } from '@/lib/constants';
import { createInitialDevice } from '@/lib/dashboard-utils';
import sseService from '@/lib/sse-service';
import { DeviceData, TestResults, TestStats } from '@/lib/types';
import { useEffect, useState } from 'react';

export function useDeviceData() {
  // Device data state
  const [devices, setDevices] = useState<DeviceData[]>(
    Array.from({ length: 9 }, (_, index) => createInitialDevice(index + 1))
  );

  // PCB status array
  const [pcbStatusArray, setPcbStatusArray] = useState<boolean[]>([]);

  // Battery levels array
  const [batteryLevels, setBatteryLevels] = useState<number[]>([]);

  // Test results state
  const [testResults, setTestResults] = useState<TestResults>({});

  // Test in progress state
  const [testInProgress, setTestInProgress] = useState<boolean>(false);

  // Serial number state
  const [serialNumber, setSerialNumber] = useState<string>('');

  // Test stats state
  const [testStats, setTestStats] = useState<TestStats>({
    duration: 0,
    panelsTested: 0,
    pcbsTested: 0,
    successPcbs: 0,
    failurePcbs: 0,
  });

  // Completion trigger state
  const [completionTrigger, setCompletionTrigger] = useState(false);

  // Connect to SSE service
  useEffect(() => {
    sseService.connect();

    // Register message handler
    sseService.onMessage(handleSSEMessage);

    // Cleanup on unmount
    return () => {
      sseService.removeMessageHandler(handleSSEMessage);
    };
  }, []);

  // Handle PCB status message
  const handlePCBStatusMessage = (message: MQTTPCBStatusMessage) => {
    setPcbStatusArray(message.pcb_status);

    // Reset test results when a new test starts
    setTestResults({});

    // Log test reset for debugging
    console.log('Test reset detected - resetting all states and test results');

    // Ensure we have at least 9 devices, but expand if needed
    const totalDeviceCount = Math.max(9, message.pcb_status.length);

    setDevices(prevDevices => {
      // Create a new array with the correct size
      let newDevices: DeviceData[] = [...prevDevices];

      // If we need more devices than we currently have, add them
      if (totalDeviceCount > prevDevices.length) {
        console.log(`Expanding device array from ${prevDevices.length} to ${totalDeviceCount}`);
        const additionalDevices = Array.from(
          { length: totalDeviceCount - prevDevices.length },
          (_, i) => createInitialDevice(prevDevices.length + i + 1)
        );
        newDevices = [...prevDevices, ...additionalDevices];
      }

      // Update all devices based on PCB status
      return newDevices.map((device, idx) => {
        // Common reset values for all devices
        const resetDevice = {
          ...device,
          // Reset all state tracking flags when a new test starts
          hasState3Occurred: false,
          isCharging: false,
          isDischarging: false,
          isChargingAndDischarging: false,
          progress: 100,
          timeRemaining: 0,
          chargingVoltage: 0,
          chargingCurrent: 0,
          dischargingVoltage: 0,
          dischargingCurrent: 0,
          // Reset all status indicators
          chargingStatus: STATUS.WARNING,
          dischargingStatus: STATUS.WARNING,
          chargingAndDischargingStatus: STATUS.WARNING,
          chargingStatusMessage: 'Waiting for test to start',
          dischargingStatusMessage: 'Waiting for test to start',
          chargingAndDischargingStatusMessage: 'Waiting for test to start',
        };

        // If this device is beyond the pcb_status array, still reset it but keep its PCB status
        if (idx >= message.pcb_status.length) {
          return resetDevice;
        }

        const pcbPresent = message.pcb_status[idx];

        if (!pcbPresent) {
          // If no PCB is present, set appropriate status but still reset all values
          return {
            ...resetDevice,
            batteryLevel: 0,
            testStatus: TEST_STATUS.NO_PCB,
            chargingStatusMessage: 'No PCB',
            dischargingStatusMessage: 'No PCB',
          };
        }

        // If PCB is present, set appropriate status and reset all values
        return {
          ...resetDevice,
          batteryLevel:
            typeof message.bp[idx] === 'number'
              ? Math.max(0, Math.min(100, message.bp[idx]))
              : device.batteryLevel,
          testStatus: TEST_STATUS.NOT_STARTED,
        };
      });
    });
  };

  // Handle state message
  const handleStateMessage = (message: MQTTStateMessage) => {
    // When state 4 is received (test completed), trigger the completion effect
    if (message.state === DEVICE_STATE.COMPLETED) {
      console.log('Test completed (state 4) received');

      // Delay the completion trigger to ensure all test results are processed
      setTimeout(() => {
        // Log the final test results for debugging
        console.log('Final test results after delay:', testResults);

        // Log detailed information about each device's state and test results
        console.log('Device state summary at test completion:');
        console.log('Checking if devices have "completed" status:');
        devices.forEach(device => {
          if (device.testStatus !== TEST_STATUS.NO_PCB) {
            console.log(`Device ${device.id} (${device.name}):`, {
              hasState3Occurred: device.hasState3Occurred,
              testStatus: device.testStatus,
              testResults: testResults[device.id] || 'No results',
            });
          }
        });

        setCompletionTrigger(prev => !prev);
      }, 1000); // 1000ms delay to ensure all MQTT messages are processed

      // Don't return here - continue to update device status
    }

    // Update device status based on state
    setDevices(prevDevices =>
      prevDevices.map(device => {
        // If device has no PCB, preserve that state
        if (device.testStatus === TEST_STATUS.NO_PCB) {
          return device;
        }

        // For devices with PCBs, update the test status based on state
        let newStatus: TestStatus = TEST_STATUS.NOT_STARTED;
        if (
          message.state === DEVICE_STATE.CHARGING ||
          message.state === DEVICE_STATE.DISCHARGING ||
          message.state === DEVICE_STATE.CHARGING_AND_DISCHARGING
        ) {
          newStatus = TEST_STATUS.IN_PROGRESS;
        } else if (message.state === DEVICE_STATE.COMPLETED) {
          newStatus = TEST_STATUS.COMPLETED;
          console.log(`Setting device ${device.id} (${device.name}) status to completed`);
        }

        // Handle state changes independently
        let isChargingAndDischarging = device.isChargingAndDischarging;
        let hasState3Occurred = device.hasState3Occurred;
        let isCharging = device.isCharging;
        let isDischarging = device.isDischarging;

        // Create a copy of the device to update status indicators
        let updatedDevice = { ...device };

        // Update state flags based on the current state message
        if (message.state === DEVICE_STATE.CHARGING) {
          // State 1: Charging only
          isCharging = true;
          isDischarging = false;
          isChargingAndDischarging = false;

          // Reset state 3 indicator when not in state 3
          updatedDevice.chargingAndDischargingStatus = STATUS.WARNING;
          updatedDevice.chargingAndDischargingStatusMessage = 'Waiting for combined mode';

          console.log(
            `State 1 (charging) message received for device ${device.id} (${device.name})`
          );
        } else if (message.state === DEVICE_STATE.DISCHARGING) {
          // State 2: Discharging only
          isCharging = false;
          isDischarging = true;
          isChargingAndDischarging = false;

          // Reset state 3 indicator when not in state 3
          updatedDevice.chargingAndDischargingStatus = STATUS.WARNING;
          updatedDevice.chargingAndDischargingStatusMessage = 'Waiting for combined mode';

          console.log(
            `State 2 (discharging) message received for device ${device.id} (${device.name})`
          );
        } else if (message.state === DEVICE_STATE.CHARGING_AND_DISCHARGING) {
          // State 3: Combined charging and discharging
          isCharging = true;
          isDischarging = true;
          isChargingAndDischarging = true;
          hasState3Occurred = true; // Set the flag to track that state 3 occurred during this test

          // Set state 3 indicator to active
          updatedDevice.chargingAndDischargingStatus = STATUS.OK;
          updatedDevice.chargingAndDischargingStatusMessage = 'Charging and discharging active';

          console.log(
            `State 3 (combined) message received for device ${device.id} (${device.name})`
          );

          // Update test results to ensure we track state 3 occurrence
          setTestResults(prev => {
            const currentResults = prev[device.id] || {
              chargingSuccess: undefined,
              dischargingSuccess: undefined,
              chargingAndDischargingSuccess: undefined,
            };

            return {
              ...prev,
              [device.id]: {
                ...currentResults,
                // Don't overwrite existing results, just ensure the field exists
                chargingAndDischargingSuccess:
                  currentResults.chargingAndDischargingSuccess !== undefined
                    ? currentResults.chargingAndDischargingSuccess
                    : undefined,
              },
            };
          });
        } else if (message.state === DEVICE_STATE.COMPLETED) {
          // State 4: Test completed - don't change any state flags
          console.log(
            `State 4 (completed) message received for device ${device.id} (${device.name})`
          );
        }

        return {
          ...updatedDevice,
          testStatus: newStatus,
          isCharging,
          isDischarging,
          isChargingAndDischarging,
          hasState3Occurred,
        };
      })
    );
  };

  // Handle channel data message
  const handleChannelDataMessage = (channelsData: MQTTDeviceData) => {
    // Process channel data
    setDevices(prevDevices => {
      const updatedDevices = [...prevDevices];
      let deviceGlobalIndex = 0;

      // Process each channel
      for (const channelData of channelsData) {
        const channelIndex = channelData.channel;

        // Validate channel data
        if (
          !Array.isArray(channelData.progress) ||
          !Array.isArray(channelData.elapsed_time) ||
          channelData.progress.length === 0 ||
          channelData.elapsed_time.length === 0
        ) {
          console.warn(`Invalid channel data for channel ${channelIndex}:`, channelData);
          continue;
        }

        // Get the number of devices in this channel
        const deviceCount = channelData.progress.length;
        console.log(`Processing channel ${channelIndex} with ${deviceCount} devices`);

        // Process each device in the channel
        for (
          let deviceIndexInChannel = 0;
          deviceIndexInChannel < deviceCount;
          deviceIndexInChannel++
        ) {
          // Calculate the actual device index in the devices array
          // For dynamic device count, we need to track the global device index
          const deviceArrayIndex = deviceGlobalIndex;
          deviceGlobalIndex++;

          // Skip if device index is out of bounds
          if (deviceArrayIndex >= updatedDevices.length) {
            console.warn(
              `Device index ${deviceArrayIndex} is out of bounds. Total devices: ${updatedDevices.length}`
            );
            continue;
          }

          // Get the previous device state
          const prevDevice = updatedDevices[deviceArrayIndex];

          // Skip devices with no PCB
          if (prevDevice.testStatus === TEST_STATUS.NO_PCB) {
            console.log(`Skipping device ${deviceArrayIndex} with no PCB`);
            continue;
          }

          // Check if charging data exists
          const hasChargingData =
            channelData.charging &&
            Array.isArray(channelData.charging.C) &&
            Array.isArray(channelData.charging.V) &&
            channelData.charging.C.length === deviceCount &&
            channelData.charging.V.length === deviceCount;

          // Check if discharging data exists
          const hasDischargingData =
            channelData.discharging &&
            Array.isArray(channelData.discharging.C) &&
            Array.isArray(channelData.discharging.V) &&
            channelData.discharging.C.length === deviceCount &&
            channelData.discharging.V.length === deviceCount;

          // Check if status data exists and has correct length
          const hasStatusData =
            Array.isArray(channelData.status) && channelData.status.length === deviceCount;

          // Determine if charging is active
          const isChargingActive =
            hasChargingData && channelData.charging!.C[deviceIndexInChannel] > 0.1;

          // Determine if discharging is active
          const isDischargingActive =
            hasDischargingData && channelData.discharging!.C[deviceIndexInChannel] > 0.1;

          // Determine if both charging and discharging are active (Sequence 3)
          const isChargingAndDischargingActive = isChargingActive && isDischargingActive;

          // If both charging and discharging are active, set the hasState3Occurred flag
          if (isChargingAndDischargingActive && !prevDevice.hasState3Occurred) {
            console.log(`State 3 detected for device ${prevDevice.id} (${prevDevice.name})`);

            // Ensure we update the test results to track state 3 occurrence
            setTestResults(prev => {
              const currentResults = prev[prevDevice.id] || {
                chargingSuccess: undefined,
                dischargingSuccess: undefined,
                chargingAndDischargingSuccess: undefined,
              };

              return {
                ...prev,
                [prevDevice.id]: {
                  ...currentResults,
                  // Don't overwrite existing results, just ensure the field exists
                  chargingAndDischargingSuccess:
                    currentResults.chargingAndDischargingSuccess !== undefined
                      ? currentResults.chargingAndDischargingSuccess
                      : undefined,
                },
              };
            });
          }

          // Update device with available data, keeping previous values when data is not present
          updatedDevices[deviceArrayIndex] = {
            ...prevDevice,
            // Update states based on activity, maintaining completely independent state tracking
            // Only update the state if the corresponding activity is detected
            isCharging:
              isChargingActive !== undefined ? Boolean(isChargingActive) : prevDevice.isCharging,
            isDischarging:
              isDischargingActive !== undefined
                ? Boolean(isDischargingActive)
                : prevDevice.isDischarging,
            // Only set combined flag if both are active simultaneously
            isChargingAndDischarging:
              isChargingAndDischargingActive !== undefined
                ? Boolean(isChargingAndDischargingActive)
                : prevDevice.isChargingAndDischarging,
            // Update hasState3Occurred flag if both charging and discharging are active
            // This flag should persist once set until test completion
            hasState3Occurred: Boolean(
              prevDevice.hasState3Occurred || isChargingAndDischargingActive
            ),
            progress: channelData.progress[deviceIndexInChannel],
            timeRemaining: Math.max(0, channelData.elapsed_time[deviceIndexInChannel]), // Ensure time is never negative
            // Update charging values only if charging data exists AND only charging is active (not combined with discharging)
            ...(hasChargingData &&
              isChargingActive &&
              !isDischargingActive && {
                chargingVoltage: channelData.charging!.V[deviceIndexInChannel],
                chargingCurrent: channelData.charging!.C[deviceIndexInChannel],
                // Only update charging status when in state 1 (charging only)
                chargingStatus: hasStatusData
                  ? channelData.status[deviceIndexInChannel]
                    ? STATUS.OK
                    : STATUS.ERROR
                  : prevDevice.chargingStatus,
                chargingStatusMessage: hasStatusData
                  ? channelData.status[deviceIndexInChannel]
                    ? 'Charging normally'
                    : 'Charging error detected'
                  : prevDevice.chargingStatusMessage,
              }),
            // Always update charging voltage/current if data exists, but don't change status
            ...(hasChargingData &&
              !(isChargingActive && !isDischargingActive) && {
                chargingVoltage: channelData.charging!.V[deviceIndexInChannel],
                chargingCurrent: channelData.charging!.C[deviceIndexInChannel],
              }),
            // Update discharging values only if discharging data exists AND only discharging is active (not combined with charging)
            ...(hasDischargingData &&
              isDischargingActive &&
              !isChargingActive && {
                dischargingVoltage: channelData.discharging!.V[deviceIndexInChannel],
                dischargingCurrent: channelData.discharging!.C[deviceIndexInChannel],
                // Only update discharging status when in state 2 (discharging only)
                dischargingStatus: hasStatusData
                  ? channelData.status[deviceIndexInChannel]
                    ? STATUS.OK
                    : STATUS.ERROR
                  : prevDevice.dischargingStatus,
                dischargingStatusMessage: hasStatusData
                  ? channelData.status[deviceIndexInChannel]
                    ? 'Discharging normally'
                    : 'Discharging error detected'
                  : prevDevice.dischargingStatusMessage,
              }),
            // Always update discharging voltage/current if data exists, but don't change status
            ...(hasDischargingData &&
              !(isDischargingActive && !isChargingActive) && {
                dischargingVoltage: channelData.discharging!.V[deviceIndexInChannel],
                dischargingCurrent: channelData.discharging!.C[deviceIndexInChannel],
              }),
            // Update charging and discharging combined status (Sequence 3) ONLY when both are active simultaneously
            ...(isChargingAndDischargingActive && {
              chargingAndDischargingStatus: hasStatusData
                ? channelData.status[deviceIndexInChannel]
                  ? STATUS.OK
                  : STATUS.ERROR
                : prevDevice.chargingAndDischargingStatus,
              chargingAndDischargingStatusMessage: hasStatusData
                ? channelData.status[deviceIndexInChannel]
                  ? 'Charging and discharging normally'
                  : 'Charging and discharging error detected'
                : prevDevice.chargingAndDischargingStatusMessage,
            }),
            // Reset state 3 indicator when state 3 is not active
            ...(!isChargingAndDischargingActive && {
              chargingAndDischargingStatus: STATUS.WARNING,
              chargingAndDischargingStatusMessage: 'Waiting for combined mode',
            }),
          };

          // Update test results based on charging and discharging status
          if (prevDevice.testStatus === TEST_STATUS.IN_PROGRESS && hasStatusData) {
            setTestResults(prev => {
              // Get current test results for this device
              const currentResults = prev[prevDevice.id] || {
                chargingSuccess: undefined,
                dischargingSuccess: undefined,
                chargingAndDischargingSuccess: undefined,
              };

              // Create a copy of the current results to avoid modifying them directly
              let updatedResults = { ...currentResults };

              // Log the current state for debugging
              console.log(`Updating test results for device ${prevDevice.id}:`, {
                isChargingActive,
                isDischargingActive,
                isChargingAndDischargingActive,
                currentStatus: channelData.status[deviceIndexInChannel],
                currentResults,
              });

              // Update each state's success status independently
              // ONLY update the specific state that is currently active

              // For charging (state 1) - update only if ONLY charging is active (not combined with discharging)
              if (hasChargingData && isChargingActive && !isDischargingActive) {
                updatedResults.chargingSuccess = channelData.status[deviceIndexInChannel];
                console.log(
                  `Device ${prevDevice.id}: Updated charging success to ${updatedResults.chargingSuccess}`
                );
              }

              // For discharging (state 2) - update only if ONLY discharging is active (not combined with charging)
              if (hasDischargingData && isDischargingActive && !isChargingActive) {
                updatedResults.dischargingSuccess = channelData.status[deviceIndexInChannel];
                console.log(
                  `Device ${prevDevice.id}: Updated discharging success to ${updatedResults.dischargingSuccess}`
                );
              }

              // For combined charging and discharging (state 3)
              // Update the combined status only if both are active simultaneously
              if (isChargingAndDischargingActive) {
                updatedResults.chargingAndDischargingSuccess =
                  channelData.status[deviceIndexInChannel];
                console.log(
                  `Device ${prevDevice.id}: Updated combined success to ${updatedResults.chargingAndDischargingSuccess}`
                );
              }

              return {
                ...prev,
                [prevDevice.id]: updatedResults,
              };
            });
          }
        }
      }

      return updatedDevices;
    });
  };

  return {
    devices,
    pcbStatusArray,
    testResults,
    completionTrigger,
    setCompletionTrigger,
  };
}
