'use client';

import { useEffect, useState } from 'react';
import { Gauge, RefreshCw } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

// Import types and utilities
import { DeviceData, BatteryData } from '@/lib/types';
import { formatTime, deviceToBatteryData, ACTIVITY_TYPE } from '@/lib/dashboard-utils';

// Import custom components
import { MetricCard } from '@/components/dashboard/metric-card';
import { BatteryIndicator } from '@/components/dashboard/battery-indicator';

// Import icons
import { Battery, BatteryCharging, BoltIcon, Clock, Zap } from 'lucide-react';

interface PCBDashboardProps {
  device: DeviceData | null;
}

export default function PCBDashboard({ device }: PCBDashboardProps) {
  // State management with proper typing using our utility function
  const [batteryData, setBatteryData] = useState<BatteryData>(() => deviceToBatteryData(device));

  // Update battery data when device changes
  useEffect(() => {
    setBatteryData(deviceToBatteryData(device));
  }, [device]);

  return (
    <div className="p-4 bg-white/80 backdrop-blur-sm">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Charging Status */}
        <MetricCard
          icon={BatteryCharging}
          title="Charging Status"
          value=""
          isActive={batteryData.chargingStatus || batteryData.chargingAndDischargingStatus}
          type={ACTIVITY_TYPE.CHARGING}
          isStatus={true}
        />

        {/* Charging Voltage */}
        <MetricCard
          icon={Zap}
          title="Charging Voltage"
          value={`${batteryData.chargingVoltage.toFixed(2)} V`}
          isActive={batteryData.chargingStatus || batteryData.chargingAndDischargingStatus}
          type={ACTIVITY_TYPE.CHARGING}
        />

        {/* Charging Current */}
        <MetricCard
          icon={BoltIcon}
          title="Charging Current"
          value={`${batteryData.chargingCurrent.toFixed(2)} A`}
          isActive={batteryData.chargingStatus || batteryData.chargingAndDischargingStatus}
          type={ACTIVITY_TYPE.CHARGING}
        />

        {/* Discharging Status */}
        <MetricCard
          icon={Battery}
          title="Discharging Status"
          value=""
          isActive={batteryData.dischargingStatus || batteryData.chargingAndDischargingStatus}
          type={ACTIVITY_TYPE.DISCHARGING}
          isStatus={true}
        />

        {/* Charging and Discharging Combined Status (Sequence 3) */}
        {batteryData.chargingAndDischargingStatus && (
          <MetricCard
            icon={RefreshCw}
            title="Charging & Discharging"
            value=""
            isActive={batteryData.chargingAndDischargingStatus}
            type={ACTIVITY_TYPE.COMBINED}
            isStatus={true}
            activeText="Active"
          />
        )}

        {/* Discharging Voltage */}
        <MetricCard
          icon={Zap}
          title="Discharging Voltage"
          value={`${batteryData.dischargingVoltage.toFixed(2)} V`}
          isActive={batteryData.dischargingStatus || batteryData.chargingAndDischargingStatus}
          type={ACTIVITY_TYPE.DISCHARGING}
        />

        {/* Discharging Current */}
        <MetricCard
          icon={BoltIcon}
          title="Discharging Current"
          value={`${batteryData.dischargingCurrent.toFixed(2)} A`}
          isActive={batteryData.dischargingStatus || batteryData.chargingAndDischargingStatus}
          type={ACTIVITY_TYPE.DISCHARGING}
        />

        {/* Discharging Time */}
        <MetricCard
          icon={Clock}
          title="Discharging Time"
          value={formatTime(batteryData.dischargingTime)}
          isActive={batteryData.dischargingStatus || batteryData.chargingAndDischargingStatus}
          type={ACTIVITY_TYPE.DISCHARGING}
        />

        {/* Charging Time */}
        <MetricCard
          icon={Clock}
          title="Charging Time"
          value={formatTime(batteryData.chargingTime)}
          isActive={batteryData.chargingStatus || batteryData.chargingAndDischargingStatus}
          type={ACTIVITY_TYPE.CHARGING}
        />

        {/* Battery Icon */}
        <Card className="relative overflow-hidden shadow-md bg-white border border-slate-200">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <BatteryIndicator
              batteryLevel={batteryData.batteryLevel}
              isCharging={batteryData.chargingStatus}
              isChargingAndDischarging={batteryData.chargingAndDischargingStatus}
              hasPCB={true} // Always true for PCB dashboard as it's showing a specific PCB
            />
            <div className="mt-4">
              <Gauge className="h-8 w-8" />
              <p className="text-2xl font-bold mt-1">{batteryData.batteryLevel}%</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
