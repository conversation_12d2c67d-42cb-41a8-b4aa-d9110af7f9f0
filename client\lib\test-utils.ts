'use client';

import { DeviceData, TestReport, TestResults, TestStats } from './types';

/**
 * Convert TestStats to TestReport format
 * This utility function standardizes the conversion between TestStats and TestReport
 */
export function statsToReport(stats: TestStats, serialNumber: string, variant: string): TestReport {
  const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  const id = `rep-${Date.now().toString().slice(-6)}`;

  return {
    id,
    date,
    variant,
    serialNumber,
    successPcbs: stats.successPcbs,
    failurePcbs: stats.failurePcbs,
  };
}

/**
 * Convert TestReport to partial TestStats
 * This is useful when you need to display report data in stats format
 */
export function reportToStats(report: TestReport): Partial<TestStats> {
  return {
    successPcbs: report.successPcbs,
    failurePcbs: report.failurePcbs,
    pcbsTested: report.successPcbs + report.failurePcbs,
    // Note: duration and panelsTested are not available in TestReport
  };
}

/**
 * Calculate test success rate as a percentage
 */
export function calculateSuccessRate(stats: TestStats | TestReport): number {
  // Get success count
  const successPcbs = stats.successPcbs;

  // Get total count
  const totalCount =
    'pcbsTested' in stats && stats.pcbsTested !== undefined
      ? stats.pcbsTested
      : stats.successPcbs + stats.failurePcbs;

  if (totalCount === 0) return 0;
  return Math.round((successPcbs / totalCount) * 100);
}

/**
 * Format test duration from seconds to a readable string
 */
export function formatTestDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes < 60) {
    return `${minutes}m ${remainingSeconds}s`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  return `${hours}h ${remainingMinutes}m ${remainingSeconds}s`;
}

/**
 * Count success and failure from test results
 */
export function countTestResults(
  devices: DeviceData[],
  testResults: TestResults
): { successPcbs: number; failurePcbs: number } {
  let successPcbs = 0;
  let failurePcbs = 0;

  // Count successes and failures from testResults
  Object.entries(testResults).forEach(([deviceId, result]) => {
    const deviceIndex = parseInt(deviceId) - 1;
    const device = devices[deviceIndex];

    if (!device) return;

    // Check if state 3 occurred during the test
    const state3Occurred = device?.hasState3Occurred || false;

    if (state3Occurred) {
      // If state 3 occurred, all three success flags must be true for success
      const chargingSuccess = result.chargingSuccess === true;
      const dischargingSuccess = result.dischargingSuccess === true;
      const combinedSuccess = result.chargingAndDischargingSuccess === true;

      if (chargingSuccess && dischargingSuccess && combinedSuccess) {
        successPcbs++;
      } else {
        failurePcbs++;
      }
    } else {
      // If state 3 didn't occur, only charging and discharging success flags are required
      const chargingSuccess = result.chargingSuccess === true;
      const dischargingSuccess = result.dischargingSuccess === true;

      if (chargingSuccess && dischargingSuccess) {
        successPcbs++;
      } else {
        failurePcbs++;
      }
    }
  });

  return { successPcbs, failurePcbs };
}
