/**
 * Test script for MQTT monitoring service
 *
 * This script simulates MQTT messages for testing the monitoring service
 * without requiring a real MQTT broker.
 */

const mqtt = require('mqtt');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// MQTT connection configuration
const MQTT_CONFIG = {
  url: process.env.MQTT_URL || 'ws://*************:9001/',
  options: {
    username: process.env.MQTT_USERNAME || 'resonate',
    password: process.env.MQTT_PASSWORD || 'Resonate@123',
  },
  topic: process.env.MQTT_TOPIC || 'pico/testing',
};

// Device states
const DEVICE_STATE = {
  CHARGING: 1,
  DISCHARGING: 2,
  CHARGING_AND_DISCHARGING: 3,
  COMPLETED: 4,
};

// Connect to MQTT broker
console.log(`Connecting to MQTT broker at ${MQTT_CONFIG.url}...`);
const client = mqtt.connect(MQTT_CONFIG.url, MQTT_CONFIG.options);

// Handle connection events
client.on('connect', () => {
  console.log('Connected to MQTT broker');

  // Start the test sequence
  simulateTestSequence();
});

client.on('error', err => {
  console.error('MQTT connection error:', err);
});

// Simulate a test sequence
async function simulateTestSequence() {
  try {
    // Simulate PCB status message for first test (6 devices with PCBs)
    // All devices in this test will have the same serial number (SN-671168)
    console.log('Publishing PCB status message for first test...');
    const pcbStatusMessage1 = {
      pcb_status: [true, true, true, false, false, false, true, true, true],
      bp: [12, 34, 79, 73, 94, 62, 19, 89, 6],
    };
    await publishMessage(pcbStatusMessage1);
    await delay(1000);

    // Simulate state 1 (charging)
    console.log('Publishing state 1 (charging) message...');
    const state1Message = { state: DEVICE_STATE.CHARGING };
    await publishMessage(state1Message);
    await delay(1000);

    // Simulate channel data for charging (state 1)
    console.log('Publishing channel data for charging (state 1)...');
    const chargingData = [
      {
        channel: 1,
        progress: [50, 50, 50],
        elapsed_time: [15, 15, 15],
        charging: {
          C: [4.86, 0.6, 9.29],
          V: [9.17, 8.96, 2.04],
        },
        status: [false, true, true],
      },
      {
        channel: 2,
        progress: [50, 50, 50],
        elapsed_time: [15, 15, 15],
        charging: {
          C: [3.64, 4.68, 3.25],
          V: [3.78, 7.26, 1.71],
        },
        status: [true, true, true],
      },
      {
        channel: 3,
        progress: [50, 50, 50],
        elapsed_time: [15, 15, 15],
        charging: {
          C: [7.45, 8.53, 1.67],
          V: [3.17, 2.44, 4.5],
        },
        status: [true, true, true],
      },
    ];
    await publishMessage(chargingData);
    await delay(2000);

    // Simulate state 2 (discharging)
    console.log('Publishing state 2 (discharging) message...');
    const state2Message = { state: DEVICE_STATE.DISCHARGING };
    await publishMessage(state2Message);
    await delay(1000);

    // Simulate channel data for discharging (state 2)
    console.log('Publishing channel data for discharging (state 2)...');
    const dischargingData = [
      {
        channel: 1,
        progress: [50, 50, 50],
        elapsed_time: [15, 15, 15],
        discharging: {
          C: [0.12, 6.8, 6.29],
          V: [5.95, 5.5, 8],
        },
        status: [false, false, true],
      },
      {
        channel: 2,
        progress: [50, 50, 50],
        elapsed_time: [15, 15, 15],
        discharging: {
          C: [5.45, 4.53, 0.89],
          V: [4.16, 7.69, 4.23],
        },
        status: [false, false, true],
      },
      {
        channel: 3,
        progress: [50, 50, 50],
        elapsed_time: [15, 15, 15],
        discharging: {
          C: [6.81, 5.2, 6.49],
          V: [7.09, 5.04, 3.12],
        },
        status: [true, true, true],
      },
    ];
    await publishMessage(dischargingData);
    await delay(2000);

    // For the first test, we'll skip state 3 (charging and discharging) to match the example
    console.log(
      'Note: Intentionally skipping state 3 (charging and discharging) for the first test to demonstrate "NA" status'
    );

    // Simulate state 4 (completed) for first test
    console.log('Publishing state 4 (completed) message for first test...');
    const state4Message = { state: DEVICE_STATE.COMPLETED };
    await publishMessage(state4Message);
    await delay(3000);

    // Simulate a second test with different PCB configuration
    console.log('Starting second test...');

    // Simulate PCB status message for second test (3 devices with PCBs)
    // All devices in this test will have the same serial number (SN-272246)
    console.log('Publishing PCB status message for second test...');
    const pcbStatusMessage2 = {
      pcb_status: [true, true, true, false, false, false, false, false, false],
      bp: [80, 82, 75, 0, 0, 0, 0, 0, 0],
    };
    await publishMessage(pcbStatusMessage2);
    await delay(1000);

    // For the second test, only simulate state 1 (charging) and skip states 2 and 3
    // This will test the "NA" status for states that don't occur
    console.log('Publishing state 1 (charging) message for second test...');
    await publishMessage(state1Message);
    await delay(1000);

    // Simulate channel data for charging in second test
    console.log('Publishing channel data for charging in second test...');
    const chargingData2 = [
      {
        channel: 1,
        progress: [60, 65, 70],
        elapsed_time: [35, 40, 45],
        charging: {
          V: [11.9, 12.0, 12.1],
          C: [0.4, 0.5, 0.6],
        },
        status: [true, true, true],
      },
    ];
    await publishMessage(chargingData2);
    await delay(2000);

    console.log(
      'Note: Intentionally skipping state 2 (discharging) and state 3 (charging and discharging) for the second test to demonstrate "NA" status'
    );

    // Simulate state 4 (completed) for second test
    console.log('Publishing state 4 (completed) message for second test...');
    await publishMessage(state4Message);
    await delay(2000);

    // Disconnect from MQTT broker
    console.log('All test sequences completed. Disconnecting...');
    client.end();
    process.exit(0);
  } catch (error) {
    console.error('Error simulating test sequence:', error);
    client.end();
    process.exit(1);
  }
}

// Helper function to publish a message
function publishMessage(message) {
  return new Promise((resolve, reject) => {
    client.publish(MQTT_CONFIG.topic, JSON.stringify(message), err => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

// Helper function to delay execution
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
