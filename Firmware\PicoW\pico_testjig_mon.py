import ujson

from micropython import const

# Device voltage specifications as constants
VOLTAGE_5V = const(5)
VOLTAGE_9V = const(9)
VOLTAGE_12V = const(12)

# Test duration constants (in seconds)
TEST_DURATION_CHARGING = const(30)
TEST_DURATION_DISCHARGING = const(30)


# Global variable for test duration (in seconds)
test_duration = 30  # Default value


# Device specifications as structured dictionaries
DEVICE_SPECS = {
    VOLTAGE_5V: {
        "MIN_V": 4.75,
        "MAX_V": 5.25,
        "MIN_C": 0.1,
        "MAX_C": 0.62,
        "BAT_COMB": 1.2,
        "FLUCTUATION": 0.05
    },
    VOLTAGE_9V: {
        "MIN_V": 8.55,
        "MAX_V": 9.45,
        "MIN_C": 0.1,
        "MAX_C": 0.62,
        "BAT_COMB": 2.1,
        "FLUCTUATION": 0.05
    },
    VOLTAGE_12V: {
        "MIN_V": 11.4,
        "MAX_V": 12.6,
        "MIN_C": 0.1,
        "MAX_C": 0.85,
        "BAT_COMB": 3.1,
        "FLUCTUATION": 0.05
    }
}

class TestJigMonitor:
    """
    Class to handle test jig monitoring and status calculation.
    Using a class to encapsulate state and behavior.
    """
    
    def __init__(self):
        """Initialize the TestJigMonitor with default values."""
        self.previous_display_data = [None, None, None]  # One slot for each channel
        self.config = {}  # Will be populated in initialize()
        
    def initialize(self, model_voltage):
        """
        Initialize the monitor with the appropriate device specifications.
        
        Args:
            model_voltage (int): Voltage level of the device being tested (5V, 9V, or 12V)
            
        Returns:
            bool: True if initialization is successful, False otherwise
        """
        
        # All this config selection should come from the web
        try:
            # Select the appropriate configuration based on model voltage
            if model_voltage == VOLTAGE_5V:
                self.config = DEVICE_SPECS[VOLTAGE_5V].copy()
            elif model_voltage == VOLTAGE_9V:
                self.config = DEVICE_SPECS[VOLTAGE_9V].copy()
            elif model_voltage == VOLTAGE_12V:
                self.config = DEVICE_SPECS[VOLTAGE_12V].copy()
            else:
                print(f"Error: Unsupported model voltage: {model_voltage}V")
                return False
                
            return True
            
        except Exception as e:
            print(f"Error initializing TestJigMonitor: {e}")
            return False

    def _safe_get(self, data, key, index=None, default=0):
        """
        Safely get a value from nested data structures.
        
        Args:
            data (dict): Data structure to extract from
            key (str): Key to look up
            index (int, optional): Index to look up if result is a list
            default: Default value to return if key/index not found
            
        Returns:
            Value at the specified key/index or default if not found
        """
        try:
            if data is None:
                return default
                
            value = data.get(key, default)
            
            if index is not None and isinstance(value, list) and len(value) > index:
                return value[index]
                
            return value
        except Exception:
            return default

    def compute_charging_status_and_progress(self, channels_data, duration):
        """
        Compute charging status and progress for the channels.
        
        Args:
            channels_data (dict): Data for channels being monitored
            duration (int): Duration of the current monitoring period in seconds
            
        Returns:
            dict: Updated channels data with status and progress information
        """
        
        global test_duration
        
        try:
            # Validate input
            if 'charging' not in channels_data or 'channel' not in channels_data:
                print("Error: Missing required keys in channels_data")
                return channels_data
                
            # Get the charging channels data
            charging_channels = channels_data['charging']
            channel_index = int(channels_data.get("channel", 1)) - 1  # Convert to 0-based index
            
            #print("previous data", self.previous_display_data)
            #print("channels_data", charging_channels)
            
            # Check if channel_index is valid for our previous_display_data list
            if channel_index < 0 or channel_index >= len(self.previous_display_data):
                print(f"Error: Invalid channel index {channel_index}")
                return channels_data
            
            
            #print("charging_channels", charging_channels)
            
            # Get previous data for this channel
            previous_channel_data = self.previous_display_data[channel_index]
            
            # Initialize status, progress, and elapsed_time lists
            channels_data['status'] = [False] * 3
            channels_data['progress'] = [0] * 3
            channels_data['elapsed_time'] = [0] * 3
            
            
            #print("channels_data", channels_data)
            
            # Get voltage array safely
            voltage_array = charging_channels.get("V", [])
            
            if not voltage_array:
                voltage_array = [0, 0, 0]
                
            
            # For each channel, compute status and progress
            for channel_idx in range(min(len(voltage_array), 3)):
                
                # Get current reading, defaulting to 0 if not available
                try: # {"V": [0, 0, 0], "C": [0, 0, 0]}
                    current_value = float(self._safe_get(charging_channels, 'C', channel_idx, 0))
                    print("channel_idx & current_value", channel_idx, current_value)
                    # Check if current is within specification range
                    if self.config["MIN_C"] <= current_value <= self.config["MAX_C"]:
                        
                        # Default values
                        progress = 0
                        elapsed_time = 0
                        status = True
                        
                        # Process if we have previous data for this channel
                        if previous_channel_data is not None:
                            prev_charging = self._safe_get(previous_channel_data, "charging", None)
                            
                            if prev_charging:
                                # Get previous current value
                                prev_current = float(self._safe_get(prev_charging, 'C', channel_idx, 0))
                                
                                # Calculate fluctuation only if previous current is in the specification range
                                if prev_current >= self.config["MIN_C"]:
                                    fluctuation = abs(prev_current - current_value)
                                    
                                    # Handle excessive fluctuation
                                    prev_status = self._safe_get(previous_channel_data, "status", channel_idx, False)
                                    prev_progress = self._safe_get(previous_channel_data, "progress", channel_idx, 0)
                                    
                                    if (fluctuation > self.config["FLUCTUATION"] and 
                                        prev_status and prev_progress < 100):
                                        status = False
                                        elapsed_time = 0
                                        progress = 100
                                        
                                    # If previously the status is False but current is in range
                                    elif not prev_status:
                                        status = prev_status
                                        elapsed_time = self._safe_get(previous_channel_data, "elapsed_time", channel_idx, 0)
                                        progress = prev_progress
                                        
                                    # If previous status is True and current is in range
                                    elif prev_status:
                                        prev_charging_current = float(self._safe_get(
                                            prev_charging, 'C', channel_idx, 0))
                                        
                                        if 0.1 <= prev_charging_current <= self.config["MAX_C"]:
                                            status = True
                                            elapsed_time = self._safe_get(
                                                previous_channel_data, "elapsed_time", channel_idx, 0) + duration
                                            
                                            # Calculate the progress
                                            progress = round((elapsed_time * 100) / test_duration)
                                            
                                            # Cap progress at 100%
                                            if progress >= 100:
                                                progress = 100
                    else:
                        
                        # Not meeting specs - default to failure state
                        progress = 100
                        status = False
                        elapsed_time = 0
                        
                        # Check if testing was completed in previous iteration
                        if previous_channel_data is not None:
                            prev_progress = self._safe_get(previous_channel_data, "progress", channel_idx, 0)
                            
                            if prev_progress >= 100:
                                progress = prev_progress
                                status = self._safe_get(previous_channel_data, "status", channel_idx, False)
                                elapsed_time = self._safe_get(previous_channel_data, "elapsed_time", channel_idx, 0)
                                
                except (ValueError, TypeError) as e:
                    # Handle conversion errors
                    print(f"Error processing channel {channel_idx}: {e}")
                    progress = 0
                    status = False
                    elapsed_time = 0
                
                # Update channel data
                channels_data["elapsed_time"][channel_idx] = elapsed_time
                channels_data["progress"][channel_idx] = progress
                channels_data["status"][channel_idx] = status
            
            # Store current channels_data as the previous data for this channel
            try:
                         
                self.previous_display_data[channel_index] = ujson.loads(ujson.dumps(channels_data))
                
                # Reset previous data if all channels completed
                if channels_data.get('progress', []).count(100) == 3:
                    self.previous_display_data[channel_index] = None
                    
            except Exception as e:
                print(f"Error saving previous data: {e}")
                
            return channels_data
                
        except Exception as e:
            print(f"Error in compute_charging_status_and_progress: {e}")
            return channels_data

    def compute_discharging_status_and_progress(self, channels_data, duration):
        """
        Compute discharging status and progress for the channels.
        
        Args:
            channels_data (dict): Data for channels being monitored
            duration (int): Duration of the current monitoring period in seconds
            
        Returns:
            dict: Updated channels data with status and progress information
        """
        
        global test_duration
        
        try:
            # Validate input
            if 'discharging' not in channels_data or 'channel' not in channels_data:
                print("Error: Missing required keys in channels_data")
                return channels_data
                
            # Get the discharging channels data
            discharging_channels = channels_data['discharging']
            channel_index = int(channels_data.get("channel", 1)) - 1  # Convert to 0-based index
            
            # Check if channel_index is valid for our previous_display_data list
            if channel_index < 0 or channel_index >= len(self.previous_display_data):
                print(f"Error: Invalid channel index {channel_index}")
                return channels_data
            
            #print("previous data", self.previous_display_data)
            #print("channels_data", discharging_channels)
            # Get previous data for this channel
            previous_channel_data = self.previous_display_data[channel_index]
            
            discharging_voltage_array = discharging_channels.get("V", [])
            if not discharging_voltage_array:
                discharging_voltage_array = [0, 0, 0]
                
            # Initialize status, progress, and elapsed_time lists if not present
            if 'status' not in channels_data:
                channels_data['status'] = [False] * len(discharging_voltage_array)
                
            if 'progress' not in channels_data:
                channels_data['progress'] = [0] * len(discharging_voltage_array)
                
            if 'elapsed_time' not in channels_data:
                channels_data['elapsed_time'] = [0] * len(discharging_voltage_array)
        
            # For each discharging channel, compute status and progress
            for channel_idx in range(len(discharging_voltage_array)):
                try:
                    # Get voltage value safely
                    voltage = float(self._safe_get(discharging_channels, "V", channel_idx, default=0))
                    
                    # Check if voltage is within specification range
                    if self.config["MIN_V"] <= voltage <= self.config["MAX_V"]:
                        # Initialize with default values
                        progress = 100
                        elapsed_time = -15
                        status = True
                        
                        # Handle previous data if available
                        if previous_channel_data is not None:
                            prev_discharging = self._safe_get(previous_channel_data, "discharging", None)
                            
                            if prev_discharging:
                                # Check for previous low voltage condition
                                prev_voltage = float(self._safe_get(prev_discharging, "V", channel_idx, default=0))
                                prev_progress = self._safe_get(previous_channel_data, "progress", channel_idx, 0)
                                
                                if prev_voltage < self.config["MIN_V"] and prev_progress <= 0:
                                    # Reset elapsed time but retain progress
                                    elapsed_time = -15
                                    progress = prev_progress
                                    status = self._safe_get(previous_channel_data, "status", channel_idx, False)
                                    
                                elif self.config["MIN_V"] <= prev_voltage <= self.config["MAX_V"]:
                                    # Update elapsed time and calculate progress
                                    status = True
                                    elapsed_time = self._safe_get(
                                        previous_channel_data, "elapsed_time", channel_idx, 0) + duration
                                    
                                    # Calculate progress for positive elapsed time
                                    if elapsed_time >= 0:
                                        progress = round(100 - (elapsed_time * 100) / test_duration)
                                        
                                        # Cap at 0%
                                        if progress <= 0:
                                            progress = 0
                                    else:
                                        # Retain progress until elapsed time becomes positive
                                        progress = self._safe_get(previous_channel_data, "progress", channel_idx, 100)
                                        status = self._safe_get(previous_channel_data, "status", channel_idx, True)
                    else:
                        # Not meeting specs - default values
                        progress = 0
                        status = False
                        elapsed_time = -15
                        
                        # Handle special cases from previous data
                        if previous_channel_data is not None:
                            # Zero voltage case
                            current_voltage = float(self._safe_get(discharging_channels, "V", channel_idx, default=0))
                            prev_progress = self._safe_get(previous_channel_data, "progress", channel_idx, 0)
                            
                            if current_voltage == 0 and prev_progress <= 0:
                                elapsed_time = -15
                                progress = prev_progress
                                status = self._safe_get(previous_channel_data, "status", channel_idx, False)
                            elif current_voltage > 0 and prev_progress <= 0:
                                # Handle positive voltage spike
                                elapsed_time = self._safe_get(
                                    previous_channel_data, "elapsed_time", channel_idx, 0) + duration
                                
                                if elapsed_time == 0:
                                    progress = 0
                                    status = False
                                else:
                                    progress = prev_progress
                                    status = self._safe_get(previous_channel_data, "status", channel_idx, False)
                                    
                except (ValueError, TypeError) as e:
                    # Handle conversion errors
                    print(f"Error processing discharge channel {channel_idx}: {e}")
                    progress = 0
                    status = False
                    elapsed_time = -15
                
                # Update channel data
                if channel_idx < len(channels_data["elapsed_time"]):
                    channels_data["elapsed_time"][channel_idx] = elapsed_time
                    
                if channel_idx < len(channels_data["progress"]):
                    channels_data["progress"][channel_idx] = progress
                    
                if channel_idx < len(channels_data["status"]):
                    channels_data["status"][channel_idx] = status
            
            # Store current channels_data as the previous data for this channel
            try:
                self.previous_display_data[channel_index] = ujson.loads(ujson.dumps(channels_data))
                
                # Reset if all channels have completed (progress = 0)
                if channels_data.get('progress', []).count(0) == len(channels_data.get('progress', [])):
                    self.previous_display_data[channel_index] = None
            except Exception as e:
                print(f"Error saving previous data: {e}")
                
            return channels_data
                    
        except Exception as e:
            print(f"Error in compute_discharging_status_and_progress: {e}")
            return channels_data

    def compute_charging_discharging_status_and_progress(self, channels_data, duration):
        """
        Compute combined charging and discharging status and progress.
        
        Args:
            channels_data (dict): Data for channels being monitored
            duration (int): Duration of the current monitoring period in seconds
            
        Returns:
            dict: Updated channels data with status and progress information
        """
        global test_duration
        
        try:
            # Validate input
            if 'discharging' not in channels_data or 'charging' not in channels_data or 'channel' not in channels_data:
                print("Error: Missing required keys in channels_data")
                return channels_data
                
            # Get the channel data
            discharging_channels = channels_data['discharging']
            battery_channels = channels_data['charging']
            channel_index = int(channels_data.get("channel", 1)) - 1  # Convert to 0-based index
            
            # Check if channel_index is valid for our previous_display_data list
            if channel_index < 0 or channel_index >= len(self.previous_display_data):
                print(f"Error: Invalid channel index {channel_index}")
                return channels_data
            
            # Get previous data for this channel
            previous_channel_data = self.previous_display_data[channel_index]
            
            discharging_voltage_array = discharging_channels.get("V", [])
            if not discharging_voltage_array:
                discharging_voltage_array = [0, 0, 0]
                
            battery_current_array = battery_channels.get("C", [])  # Fixed: "C" instead of "V"
            if not battery_current_array:
                battery_current_array = [0, 0, 0]
            
            # Initialize status, progress, and elapsed_time lists if not present
            if 'status' not in channels_data:
                channels_data['status'] = [False] * len(discharging_voltage_array)
                
            if 'progress' not in channels_data:
                channels_data['progress'] = [0] * len(discharging_voltage_array)
                
            if 'elapsed_time' not in channels_data:
                channels_data['elapsed_time'] = [0] * len(discharging_voltage_array)
        
            # For each channel, compute status and progress
            for channel_idx in range(len(discharging_voltage_array)):
                try:
                    # Get voltage and current values safely
                    voltage = float(self._safe_get(discharging_channels, "V", channel_idx, default=0))
                    battery_current = float(self._safe_get(battery_channels, "C", channel_idx, default=0))
                    
                    # Check conditions for valid operation
                    if (self.config["MIN_V"] <= voltage <= self.config["MAX_V"] and battery_current >= 0):
                        # Initialize with default values
                        progress = 0
                        elapsed_time = 0
                        status = True
                        
                        # Handle previous data if available
                        if previous_channel_data is not None:
                            prev_progress = self._safe_get(previous_channel_data, "progress", channel_idx, 0)
                            
                            # If progress was already complete
                            if prev_progress >= 100:
                                elapsed_time = 0
                                progress = self._safe_get(previous_channel_data, "progress", channel_idx, 0)
                                status = self._safe_get(previous_channel_data, "status", channel_idx, False)
                                
                            # If conditions were met previously
                            elif self._check_previous_conditions(previous_channel_data, channel_idx):
                                status = True
                                elapsed_time = self._safe_get(
                                    previous_channel_data, "elapsed_time", channel_idx, 0) + duration
                                
                                # Calculate progress
                                progress = round((elapsed_time * 100) / test_duration)
                                
                                # Cap at 100%
                                if progress >= 100:
                                    progress = 100
                    else:
                        # Not meeting specs - default values
                        progress = 100
                        status = False
                        elapsed_time = 0
                        
                        # Handle special cases from previous data
                        if previous_channel_data is not None:
                            prev_progress = self._safe_get(previous_channel_data, "progress", channel_idx, 0)
                            
                            if prev_progress >= 100:
                                elapsed_time = 0
                                progress = self._safe_get(previous_channel_data, "progress", channel_idx, 0)
                                status = self._safe_get(previous_channel_data, "status", channel_idx, False)
                                    
                except (ValueError, TypeError) as e:
                    # Handle conversion errors
                    print(f"Error processing channel {channel_idx}: {e}")
                    progress = 0
                    status = False
                    elapsed_time = 0
                
                # Update channel data
                if channel_idx < len(channels_data["elapsed_time"]):
                    channels_data["elapsed_time"][channel_idx] = elapsed_time
                    
                if channel_idx < len(channels_data["progress"]):
                    channels_data["progress"][channel_idx] = progress
                    
                if channel_idx < len(channels_data["status"]):
                    channels_data["status"][channel_idx] = status
            
            # Store current channels_data as the previous data for this channel
            try:
                self.previous_display_data[channel_index] = ujson.loads(ujson.dumps(channels_data))
                
                # Reset if all channels have completed
                if channels_data.get('progress', []).count(100) == len(channels_data.get('progress', [])):
                    self.previous_display_data[channel_index] = None
            except Exception as e:
                print(f"Error saving previous data: {e}")
                
            return channels_data
                    
        except Exception as e:
            print(f"Error in compute_charging_discharging_status_and_progress: {e}")
            return channels_data
            
    def _check_previous_conditions(self, previous_channel_data, channel_idx):
        """
        Helper method to check if previous conditions were met.
        
        Args:
            previous_channel_data (dict): Previous channel data to check
            channel_idx (int): Index of the channel to check
            
        Returns:
            bool: True if previous conditions were met, False otherwise
        """
        try:
            # Ensure we have previous data
            if previous_channel_data is None:
                return False
                
            # Get previous voltage and current values
            prev_discharging = self._safe_get(previous_channel_data, "discharging", None)
            prev_charging = self._safe_get(previous_channel_data, "charging", None)
            
            if prev_discharging and prev_charging:
                prev_voltage = float(self._safe_get(prev_discharging, "V", channel_idx, default=0))
                prev_battery_current = float(self._safe_get(prev_charging, "C", channel_idx, default=0))
                
                # Check if conditions were met
                return (self.config["MIN_V"] <= prev_voltage <= self.config["MAX_V"] and 
                        prev_battery_current >= 0)
            return False
        except Exception:
            return False


# For backward compatibility, create a global instance and expose functions
_monitor = TestJigMonitor()

def initialize(model_voltage):
    """
    Initialize with the appropriate device specifications.
    
    Args:
        model_voltage (int): Voltage level of the device being tested
        
    Returns:
        bool: True if initialization is successful
    """
    return _monitor.initialize(model_voltage)

def compute_charging_status_and_progress(channels_data, duration):
    """
    Compute charging status and progress for the channels.
    
    Args:
        channels_data (dict): Data for channels being monitored
        duration (int): Duration of the current monitoring period
        
    Returns:
        dict: Updated channels data with status and progress
    """
    return _monitor.compute_charging_status_and_progress(channels_data, duration)

def compute_discharging_status_and_progress(channels_data, duration):
    """
    Compute discharging status and progress for the channels.
    
    Args:
        channels_data (dict): Data for channels being monitored
        duration (int): Duration of the current monitoring period
        
    Returns:
        dict: Updated channels data with status and progress
    """
    return _monitor.compute_discharging_status_and_progress(channels_data, duration)

def compute_charging_discharging_status_and_progress(channels_data, duration):
    """
    Compute combined charging and discharging status and progress.
    
    Args:
        channels_data (dict): Data for channels being monitored
        duration (int): Duration of the current monitoring period
        
    Returns:
        dict: Updated channels data with status and progress
    """
    return _monitor.compute_charging_discharging_status_and_progress(channels_data, duration)

def get_battery_combination():
    """
    Get the battery combination value from the current device configuration.
    
    Returns:
        float: Battery combination value from DEVICE_SPECS for the initialized voltage
    """
    return _monitor.config.get("BAT_COMB", 0.0)

def set_test_duration(duration):
    """
    Set the test duration for both charging and discharging operations.
    
    Args:
        duration (int): Duration in seconds for test operations
        
    Returns:
        int: The newly set duration value
    """
    global test_duration
    test_duration = duration
    return test_duration
