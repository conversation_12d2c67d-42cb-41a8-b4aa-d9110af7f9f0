'use client';

import { useState, useEffect } from 'react';
import { Wifi, WifiOff } from 'lucide-react';

interface MqttStatusProps {
  compact?: boolean;
}

export function MqttStatus({ compact = false }: MqttStatusProps) {
  const [isConnected, setIsConnected] = useState(false);

  // Simulate MQTT connection status
  useEffect(() => {
    // In a real app, this would be connected to your MQTT client
    const connected = Math.random() > 0.2; // 80% chance of being connected
    setIsConnected(connected);

    // Simulate connection changes
    const interval = setInterval(() => {
      const newStatus = Math.random() > 0.1;
      if (newStatus !== isConnected) {
        setIsConnected(newStatus);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center">
      <div
        className={`
        ${isConnected ? 'bg-green-100' : 'bg-red-100'}
        p-1 rounded-full
      `}
      >
        {isConnected ? (
          <Wifi className="h-3 w-3 text-green-600" />
        ) : (
          <WifiOff className="h-3 w-3 text-red-600" />
        )}
      </div>
      <span
        className={`ml-1 text-[10px] font-medium ${
          isConnected ? 'text-green-600' : 'text-red-600'
        }`}
      >
        {isConnected ? 'ON' : 'OFF'}
      </span>
    </div>
  );
}
