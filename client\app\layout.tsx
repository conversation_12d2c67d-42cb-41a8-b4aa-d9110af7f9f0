import type React from 'react';
import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import '../styles/progress-bar.css';
import '../styles/battery-indicator.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Battery Testing Dashboard',
  description: 'Monitor and analyze battery performance metrics',
  generator: 'v0.dev',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.className} min-h-screen bg-gradient-to-br from-slate-50 to-blue-50`}
      >
        {children}
      </body>
    </html>
  );
}
