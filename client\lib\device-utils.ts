'use client';

import { BatteryD<PERSON>, DeviceData, TestResults } from '@/lib/types';
import {
  ACTIVITY_COLORS,
  ACTIVITY_TYPE,
  ActivityType,
  STATUS,
  STYLE_TYPE,
  StyleType,
} from './constants';

/**
 * Determines if a device has failed any tests
 */
export function hasDeviceFailed(device: DeviceData, testResults?: TestResults): boolean {
  if (!testResults || !testResults[device.id]) return false;

  const result = testResults[device.id];
  const chargingSuccess = result.chargingSuccess === true;
  const dischargingSuccess = result.dischargingSuccess === true;
  const combinedSuccess = result.chargingAndDischargingSuccess === true;

  // If state 3 occurred, all three must be true for success
  if (device.hasState3Occurred) {
    return !(chargingSuccess && dischargingSuccess && combinedSuccess);
  }

  // Otherwise, just charging and discharging must be true
  return !(chargingSuccess && dischargingSuccess);
}

/**
 * Get the appropriate battery icon information based on level, charging state, and PCB status
 * Returns an object with icon type and class names
 */
export function getBatteryIconInfo(
  level: number,
  isCharging: boolean,
  isChargingAndDischarging: boolean,
  size: 'sm' | 'md' | 'lg' | 'card' = 'md',
  hasPCB: boolean = true
): { type: string; className: string } {
  // Size mapping
  const sizeMap = {
    sm: 3.5, // Smaller size
    md: 6, // Medium size
    lg: 12, // Large size
    card: 4.5, // Custom size for device card (between sm and md)
  };

  const iconSize = sizeMap[size];
  const sizeClass = `h-${iconSize} w-${iconSize}`;

  // Priority 0: No PCB - show alert circle with red
  if (!hasPCB) {
    return {
      type: 'alert-circle',
      className: `${sizeClass} text-red-600`,
    };
  }

  // Priority 1: Charging and Discharging state (combined)
  if (isChargingAndDischarging) {
    return {
      type: 'refresh-cw',
      className: `${sizeClass} text-teal-500`,
    };
  }

  // Priority 2: Charging state
  if (isCharging) {
    return {
      type: 'battery-charging',
      className: `${sizeClass} text-green-500`,
    };
  }

  // Priority 3: Battery level for discharging or idle state
  if (level < 15) {
    // Critical battery level
    return {
      type: 'battery-warning',
      className: `${sizeClass} text-red-600`,
    };
  } else if (level < 30) {
    // Very low battery
    return {
      type: 'battery-warning',
      className: `${sizeClass} text-red-500`,
    };
  } else if (level < 50) {
    // Low battery
    return {
      type: 'battery-low',
      className: `${sizeClass} text-orange-500`,
    };
  } else if (level < 75) {
    // Medium battery
    return {
      type: 'battery-medium',
      className: `${sizeClass} text-amber-400`,
    };
  } else {
    // High battery
    return {
      type: 'battery-full',
      className: `${sizeClass} text-green-500`,
    };
  }
}

/**
 * Get status icon information based on status
 * Returns an object with icon type and class names
 */
export function getStatusIconInfo(status: (typeof STATUS)[keyof typeof STATUS] | boolean): {
  type: string;
  className: string;
} {
  // Handle boolean values (true = OK, false = ERROR)
  if (typeof status === 'boolean') {
    return status
      ? { type: 'check-circle', className: 'h-5 w-5 text-green-600' }
      : { type: 'alert-circle', className: 'h-5 w-5 text-red-600' };
  }

  // Handle string status values
  switch (status) {
    case STATUS.OK:
      return { type: 'check-circle', className: 'h-5 w-5 text-green-600' };
    case STATUS.WARNING:
      return { type: 'alert-triangle', className: 'h-5 w-5 text-amber-500' };
    case STATUS.ERROR:
      return { type: 'alert-circle', className: 'h-5 w-5 text-red-600' };
    default:
      return { type: 'alert-circle', className: 'h-5 w-5 text-red-600' };
  }
}

/**
 * Get card border color based on device status
 */
export function getCardBorderColor(
  isCharging: boolean,
  isDischarging: boolean,
  isChargingAndDischarging: boolean,
  batteryLevel: number
): string {
  if (isChargingAndDischarging) {
    return 'border-l-4 border-l-teal-500';
  }

  if (isCharging) {
    return 'border-l-4 border-l-green-500';
  }

  if (isDischarging) {
    return 'border-l-4 border-l-orange-500';
  }

  if (batteryLevel <= 20) {
    return 'border-l-4 border-l-red-500';
  }

  return 'border-l-4 border-l-slate-300';
}

/**
 * Get style for a card or component based on activity type
 */
export function getComponentStyle(
  isActive: boolean,
  type: StyleType,
  variant: 'card' | 'status' = 'card'
): string {
  if (!isActive) {
    return 'shadow-sm hover:shadow-md bg-white border border-slate-200';
  }

  // Base styles for each type
  const typeStyles = {
    [STYLE_TYPE.COMBINED]: {
      card: 'shadow-sm hover:shadow-md shadow-teal-100/50 bg-gradient-to-br from-teal-50 to-teal-100 border border-teal-200',
      status:
        'shadow-sm hover:shadow-md shadow-teal-100/50 bg-gradient-to-br from-teal-50 to-teal-100 border border-teal-200',
    },
    [STYLE_TYPE.CHARGING]: {
      card: 'shadow-sm hover:shadow-md shadow-green-100/50 bg-gradient-to-br from-green-50 to-green-100 border border-green-200',
      status:
        'shadow-sm hover:shadow-md shadow-green-100/50 bg-gradient-to-br from-green-50 to-green-100 border border-green-200',
    },
    [STYLE_TYPE.DISCHARGING]: {
      card: 'shadow-sm hover:shadow-md shadow-orange-100/50 bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200',
      status:
        'shadow-sm hover:shadow-md shadow-orange-100/50 bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200',
    },
  };

  return typeStyles[type][variant];
}

/**
 * Get text color based on activity type
 */
export function getTextColor(isActive: boolean, type: StyleType): string {
  if (!isActive) {
    return 'text-muted-foreground';
  }

  const colorMap = {
    [STYLE_TYPE.COMBINED]: 'text-teal-700',
    [STYLE_TYPE.CHARGING]: 'text-green-700',
    [STYLE_TYPE.DISCHARGING]: 'text-orange-700',
  };

  return colorMap[type];
}

/**
 * Get device activity type based on its state
 */
export function getDeviceActivityType(device: DeviceData): StyleType {
  if (device.isChargingAndDischarging) {
    return STYLE_TYPE.COMBINED;
  }

  if (device.isCharging) {
    return STYLE_TYPE.CHARGING;
  }

  return STYLE_TYPE.DISCHARGING;
}

/**
 * Get progress bar color based on device state
 */
export function getProgressBarColor(device: DeviceData): string {
  if (device.isChargingAndDischarging) {
    return 'bg-gradient-to-r from-green-300 via-teal-400 to-orange-400';
  }

  if (device.isCharging) {
    return 'bg-gradient-to-r from-green-300 to-green-600';
  }

  return 'bg-gradient-to-r from-red-300 to-red-600';
}

/**
 * Convert DeviceData to BatteryData
 * This utility function standardizes the conversion between DeviceData and BatteryData
 */
export function deviceToBatteryData(device: DeviceData | null): BatteryData {
  if (!device) {
    return {
      chargingStatus: false,
      dischargingStatus: false,
      chargingAndDischargingStatus: false,
      chargingVoltage: 0,
      chargingCurrent: 0,
      dischargingVoltage: 0,
      dischargingCurrent: 0,
      dischargingTime: 0,
      chargingTime: 0,
      batteryLevel: 0,
    };
  }

  // Determine which time to use based on the current activity
  let chargingTime = 0;
  let dischargingTime = 0;

  // If the device is in charging state, use timeRemaining for chargingTime
  if (device.isCharging && !device.isDischarging && !device.isChargingAndDischarging) {
    chargingTime = device.timeRemaining;
  }
  // If the device is in discharging state, use timeRemaining for dischargingTime
  else if (!device.isCharging && device.isDischarging && !device.isChargingAndDischarging) {
    dischargingTime = device.timeRemaining;
  }
  // If the device is in charging and discharging state, use timeRemaining for both
  else if (device.isChargingAndDischarging) {
    chargingTime = device.timeRemaining;
    dischargingTime = device.timeRemaining;
  }

  return {
    chargingStatus: device.isCharging,
    dischargingStatus: device.isDischarging,
    chargingAndDischargingStatus: device.isChargingAndDischarging,
    chargingVoltage: device.chargingVoltage,
    chargingCurrent: device.chargingCurrent,
    dischargingVoltage: device.dischargingVoltage,
    dischargingCurrent: device.dischargingCurrent,
    dischargingTime: dischargingTime,
    chargingTime: chargingTime,
    batteryLevel: device.batteryLevel,
  };
}

/**
 * Get the activity type based on battery data
 */
export function getBatteryActivityType(data: BatteryData | DeviceData): ActivityType {
  // Handle both BatteryData and DeviceData
  const isCharging = 'isCharging' in data ? data.isCharging : data.chargingStatus;
  const isDischarging = 'isDischarging' in data ? data.isDischarging : data.dischargingStatus;
  const isChargingAndDischarging =
    'isChargingAndDischarging' in data
      ? data.isChargingAndDischarging
      : data.chargingAndDischargingStatus;

  if (isChargingAndDischarging) {
    return ACTIVITY_TYPE.COMBINED;
  }

  if (isCharging) {
    return ACTIVITY_TYPE.CHARGING;
  }

  if (isDischarging) {
    return ACTIVITY_TYPE.DISCHARGING;
  }

  return ACTIVITY_TYPE.INACTIVE;
}

/**
 * Get the appropriate color for an activity type
 */
export function getActivityColor(
  activityType: ActivityType,
  colorType: 'text' | 'bg' | 'icon' | 'gradient' | 'border'
): string {
  return ACTIVITY_COLORS[activityType][colorType];
}
