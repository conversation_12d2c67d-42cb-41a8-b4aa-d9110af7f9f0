
# Import modules
import utime
import machine
import ujson
from micropython import const
from machine import unique_id
import gc
import network
from umqtt.simple import MQTTClient
import ubinascii


# Import custom modules
import charging

import discharging

import pico_testjig_mon

import pico_net



# Define constants
STATE_IDLE = const(0)
STATE_CHARGING = const(1)
STATE_DISCHARGING = const(2)
STATE_CHARGING_DISCHARGING = const(3)
STATE_COMPLETED = const(4)

# MUX control constants
MUX_ENABLE = const(1)
MUX_DISABLE = const(0)

# I2C address constants
RELAY_CONFIG_PORT0 = const(0x06)
RELAY_CONFIG_PORT1 = const(0x07)
RELAY_STATE_PORT0 = const(0x02)
RELAY_STATE_PORT1 = const(0x03)

# Timing constants
TIMER_FREQUENCY = const(1)  # 1 Hz (once per second)
I2C_DELAY_MS = const(50)
MAIN_LOOP_DELAY_MS = const(1000)


# Global variables

mqtt_client = None
command = None
msg_available = False


"""
# Test sequence configuration
test_sequences = [
    
    # Seq.1: Only discharging sequence of all the 9 channels.
    {
        "charge": {"required": False, "channels": [1, 2, 3]},
        "discharge": {"required": True, "load_current": [3, 3, 3], "channels": [1, 2, 3]},
        "duration": 60
    },
    
    # Seq.2: Only charging sequence of all the 9 channels. (0.7 * 9) = 6.3 A
    {
        "charge": {"required": True, "channels": [1, 2, 3]},
        "discharge": {"required": False, "load_current": [3, 3, 3], "channels": [1, 2, 3]},
        "duration": 60
    },
    
    # Seq.3: Charging + Discharging sequence for only first three channels (0.7 + 3) * 9 = 33.3A
    {
        "charge": {"required": True, "channels": [1]},
        "discharge": {"required": True, "load_current": [3, 0, 0], "channels": [1]},
        "duration": 60
    },
    
    # Seq.4: Charging + Discharging sequence for channels 4, 5, 6
    {
        "charge": {"required": True, "channels": [2]},
        "discharge": {"required": True, "load_current": [0, 3, 0], "channels": [2]},
        "duration": 60
    },
   
    # Seq.5: Charging + Discharging sequence for channels 7, 8, 9
    {
        "charge": {"required": True, "channels": [3]},
        "discharge": {"required": True, "load_current": [0, 0, 3], "channels": [3]},
        "duration": 60
    }
]

"""
test_sequences = [                
                        {
                            "charge": {"required": False, "channels": [1, 2, 3]},
                            "discharge": {"required": True, "load_current": [2, 2, 2], "channels": [1, 2, 3]},
                            "duration": 30
                        },   
                                             
                        # Seq.2: Only charging sequence of all the 9 channels. (0.7 * 9) = 6.3 A                        
                        {
                            "charge": {"required": True, "channels": [1, 2, 3]},
                            "discharge": {"required": False, "load_current": [0.1, 0.1, 0.1], "channels": [1, 2, 3]},
                            "duration": 30
                        },
                        {
                            "charge": {"required": True, "channels": [1, 2, 3]},
                            "discharge": {"required": True, "load_current": [0.1, 0.1, 0.1], "channels": [1, 2, 3]},
                            "duration": 30
                        }        
                        
                 ]


# MQTT Client setup
def mqtt_connect(broker, port, user, password):
    """Connect to MQTT broker"""
    
    client = None
    client_id = ubinascii.hexlify(unique_id()).decode()
    
    try:
        client = MQTTClient(client_id=client_id, server=broker, port=port, user=user, password=password, keepalive=60)
        client.connect()
    
    except Exception as error:
        print(f"Error while MQTT connect: {error}")
    
    return client


def mqtt_disconnect(client):
    """Disconnect from MQTT broker"""
    
    try:
        if client != None:
            client.disconnect()
            
        print("Disconnected from MQTT broker")   
    except Exception as error:
        print(f"Error while MQTT disconnect: {error}") 
    
    return

    

def subscribe_callback(topic, msg):
    """Callback function for MQTT subscribe"""
    global msg_available, command
    
    try:
        command = msg.decode()
        print(f"Topic: {topic}\nMessage: {command}")
        
        msg_available = True
        
        
    except Exception as e:
        print(f"Error in subscribe callback: {e}")


def mqtt_subscribe(client, topic="pico/command"):
    """Subscribe to MQTT topic"""
    
    try:
        client.set_callback(subscribe_callback)
        client.subscribe(topic)
        print(f"Subscribed to {topic}")
            
    except Exception as error:
        print(f"Error while Subscribing {error}")
        return False
    
    return True


class TestSequencer:
    """Class to manage test sequences using a state machine pattern."""
    
    def __init__(self, sequences, resources, control_mux_list):
        
        """
        Initialize the TestSequencer.
        
        Args:
            sequences (list): List of test sequence configurations
            resources (dict): Hardware resources
            control_mux_list (list): MUX control pin lists
        """
        self.sequences = sequences
        self.resources = resources
        self.control_mux_list = control_mux_list
        self.current_step = 0
        self.state = STATE_IDLE
        self.display_data = []
        self.test_sequence_completion_status = []
        self.zero_current_count = 3
        self.elapsed_time = 0
        self.current_sequence = None
        self.number_of_cells_in_series = 0
        self.full_voltage = 0
        self.empty_voltage = 0
        self.isCharging = False
        
        
    def initialize(self):
        """
        Initialize the sequencer.
        
        Returns:
            bool: True if initialization is successful, False otherwise
        """
        try:
            # Monitor the first channel to detect the model voltage
            charging_data = charging.monitor(1, self.resources['i2c0'])
            
            if not charging_data:
                print("Error: Failed to get charging data for initialization")
                return False
                
            # Initialize the model being tested. Thsi data will come from the web
            #model_voltage = charging_data[0].get('V', 12)  # Default to 12V if not available
            model_voltage = 12
            
            # This needs to be changed with the model from the web.
            if not pico_testjig_mon.initialize(model_voltage=model_voltage):
                print(f"Error: Failed to initialize test jig monitor with voltage {model_voltage}V")
                return False
            
            if self.number_of_cells_in_series == 0: 
                self.number_of_cells_in_series = int(pico_testjig_mon.get_battery_combination())
                self.full_voltage = 4.2 * self.number_of_cells_in_series 
                self.empty_voltage = 2.9 * self.number_of_cells_in_series
            
            print(f"Number of cells in series: {self.number_of_cells_in_series}  Full Voltage: {self.full_voltage} and  Empty" , self.full_voltage, self.empty_voltage)
                
            return True
        
        except Exception as e:
            print(f"Error initializing sequencer: {e}")
            return False
        
    def reset(self):
        """Reset the sequencer to initial state with initial sequence and initail display data"""
        
        self.current_step = 0
        self.state = STATE_IDLE
        self.zero_current_count = 0
        self.elapsed_time = 0
        
        if self.isCharging:
            for channel_idx in range(3):
                print("11")
                charging.config(channel_idx + 1, self.resources['i2c0'], activate=False)        
        
        # Check if there are sequences
        if not self.sequences:
            print("Error: No test sequences defined")
            self.state = STATE_COMPLETED
            return
              
        # Initialize display data for first sequence
        self.current_sequence = self.sequences[self.current_step]
        self.display_data, self.test_sequence_completion_status = self._initialize_display_data(
            self.current_step, self.current_sequence)
    
    def _initialize_display_data(self, current_step, current_sequence):
        """
        Initialize the display data structure based on the current sequence.
        
        Args:
            current_step (int): Current sequence step
            current_sequence (dict): Current sequence configuration
            
        Returns:
            tuple: (display_data, test_sequence_status_list)
        """
        try:
            
            display_data = []
            
            # Initialize with battery data
            #channel_display_data = {"battery": {"V": [0, 0, 0], "C": [0, 0, 0]}}
            channel_display_data = {}
            
            channels = [1, 2, 3]
            
            # For charging present
            if "charge" in current_sequence and current_sequence["charge"].get("required", False):
                channel_display_data["charging"] = {"V": [0, 0, 0], "C": [0, 0, 0]}
                channels = current_sequence["charge"].get("channels", [1, 2, 3])
                
            # For discharging present
            if "discharge" in current_sequence and current_sequence["discharge"].get("required", False):
                channel_display_data["discharging"] = {"V": [0, 0, 0], "C": [0, 0, 0]}
            
            test_sequence_status_list = []
            for channel in channels:
                # Create a deep copy to avoid shared references
                channel_data = ujson.loads(ujson.dumps(channel_display_data))
                channel_data['channel'] = channel
                #channel_data['current_step'] = current_step
                display_data.append(channel_data)
                test_sequence_status_list.append(False)
                
            return display_data, test_sequence_status_list
        
        except Exception as e:
            print(f"Error initializing display data: {e}")
            return [], []
        
        
    def check_pcb_status(self, battery_data, charging_data, discharge_data):
        
        # Assume the Three PCB is present 
        isPCB = [True] * 3
        
        # Check each PCB's status based on battery, charging, and discharging data
        for idx in range(3): 
                     
            # Check if all three conditions are zero for this PCB
            battery_current_zero = idx < len(battery_data) and battery_data[idx].get('C', 0) == 0
            charging_current_zero = idx < len(charging_data) and charging_data[idx].get('C', 0) == 0
            discharge_voltage_zero = idx < len(discharge_data) and discharge_data[idx].get('V', 0) == 0
            
            # If all three conditions are zero, mark PCB as not present
            if battery_current_zero and charging_current_zero and discharge_voltage_zero:
                isPCB[idx] = False
            
        # Return the PCB status array
        return isPCB
        
        
    def check_battery_current(self):
        """
        Check if any of the battery channels has non-zero current.
        
        Returns:
            bool: True if any channel has current, False otherwise
        """
        try:
            battery_data_list = []
            
            for channel in range(3):
                battery_data = charging.monitor(channel + 1, self.resources['i2c1'])
                battery_data_list.extend(battery_data)
            
            print("Battery Data", battery_data_list)
                
            for battery_data in battery_data_list:
                if battery_data.get('C', 0) > 0:
                    return True
                    
            return False
        except Exception as e:
            print(f"Error checking battery current: {e}")
            return False
        
    
    def compute_battery_percentages(self, battery_data):

        percentages = []
        for cell in battery_data:
            
            voltage = cell['V']
            
            # Clamp voltage between empty and full
            clamped_voltage = max(min(voltage, self.full_voltage), self.empty_voltage)
            
            # Linear interpolation
            percentage = ((clamped_voltage - self.empty_voltage) / (self.full_voltage - self.empty_voltage)) * 100
            percentages.append(round(percentage))
        
        return percentages    

            
    def execute_sequence_step(self):
        """
        Execute the current sequence step.
        
        Returns:
            bool: True if sequence is completed, False otherwise
        """
        global mqtt_client
        
        # Check if current step is valid
        if self.current_step >= len(self.sequences):
            return True
            
            
        print("Executing sequence step")
        # Get the sequence step
        step = self.sequences[self.current_step]
        self.current_sequence = step
        
        print("Executing sequence step", self.current_sequence)
        
        try:
            
            # Configure charging if required
            if step.get("charge", {}).get("required", False):
                
                for channel in step["charge"]["channels"]:
                    charging.config(channel, self.resources['i2c0'], activate=True)
                    #charging.config(channel, self.resources['i2c1'], activate=True)
            
            
            # Configure discharging if required
            if step.get("discharge", {}).get("required", False):
                for channel_index in range(len(step['discharge']['load_current'])):
                    load_current = step["discharge"]["load_current"][channel_index]
                    discharging.config(
                        channel_index + 1, 
                        self.resources['uart0'], 
                        self.control_mux_list,
                        load_current
                    )
                    
                    utime.sleep(1)
                    discharge_data = discharging.monitor(
                channel_index + 1, self.resources['uart0'], self.control_mux_list)
                    print("discharge data in the sequence step", discharge_data)
            
            if "duration" in step:
                duration = step.get("duration", 30)
                test_duration = pico_testjig_mon.set_test_duration(duration)
                
                print("set duration is", test_duration)
                                    
            # Set the state based on the sequence
            if step.get("charge", {}).get("required", False) and step.get("discharge", {}).get("required", False):
                self.state = STATE_CHARGING_DISCHARGING                
            elif step.get("charge", {}).get("required", False):
                self.state = STATE_CHARGING
            elif step.get("discharge", {}).get("required", False):
                self.state = STATE_DISCHARGING
            else:
                self.state = STATE_IDLE
            
                        
            # Prepare status data for MQTT publishing
            status_data = {
                "sequence_number": self.current_step + 1,
                "state": self.state,

            }
                       
            try:
                if mqtt_client != None:
                    mqtt_client.publish('pico/testjig', ujson.dumps(status_data))
            except Exception as error:
                print(f"Failed to publish current state {error}")
                mqtt_disconnect()
                mqtt_client = None
                
            return False
        
        except Exception as e:
            print(f"Error executing sequence step: {e}")
            
            # Deactivate channels in case of error
            self.deactivate_channels(step)
            return True
            
    def deactivate_channels(self, sequence):
        """
        Deactivate all channels for the given sequence.
        
        Args:
            sequence (dict): Sequence configuration
        """
        try:
            
            # Deactivate charging channels
            #if sequence.get('charge', {}).get('required', False):
            for channel in sequence['charge']['channels']:
                print("Turning off inside deactivate")
                charging.config(channel=channel, i2c=self.resources['i2c0'], activate=False)
                self.isCharging = False
                    #charging.config(channel=channel, i2c=self.resources['i2c1'], activate=False)
            
                    
            # Deactivate discharging channels
            #if sequence.get('discharge', {}).get('required', False):
            load_current = 0
            for channel_index in range(len(sequence['discharge']['load_current'])):
                discharging.config(
                    channel_index + 1, 
                    self.resources['uart0'], 
                    self.control_mux_list,
                    load_current
                )
                
        except Exception as e:
            print(f"Error deactivating channels: {e}")
   
    #@micropython.native         
    def update(self, duration):
        """
        Update the sequencer state based on elapsed time.
        
        Args:
            duration (int): Elapsed duration in seconds
            
        Returns:
            bool: True if test is completed, False otherwise
        """

        global mqtt_client
        
        
        # Check if test is completed
        if self.state == STATE_COMPLETED:
            
            # Check for battery current to detect new panel
            has_current = self.check_battery_current()
           
            print("has current", has_current, self.zero_current_count)
             
            if not has_current:
                self.zero_current_count += 1
                
            # If previously all battery channels had zero current and now current is present. This is the start of the new sequence. Lets publish the JSON.
            if self.zero_current_count >= 3 and has_current:
                
                
                print("Inside condition has current")
                
                
                battery_publish_data = {'bp': [], 'pcb_status': []}
                
                # Calculate the battery percentage
                for channel_idx in [1, 2, 3]:
                    
                    battery_data = charging.monitor(channel_idx, self.resources['i2c1']) 
                    charging_data =  charging.monitor(channel_idx, self.resources['i2c0'])  
                    discharge_data = discharging.monitor(
                        channel_idx, self.resources['uart0'], self.control_mux_list)  
                    
                    # This is the duplicate data  Need to to remove after the demo. 
                    if channel_idx == 1:
                        battery_data[1] = battery_data[0]
                        battery_data[2] = battery_data[0]
                        charging_data[1] = charging_data[0]
                        charging_data[2] = charging_data[0] 
                        discharge_data[1] = discharge_data[0]
                        discharge_data[2] = discharge_data[0]
                    
                    elif channel_idx == 2:
                        battery_data[0] = battery_data[1]
                        battery_data[2] = battery_data[1]
                        charging_data[0] = charging_data[1]
                        charging_data[2] = charging_data[1]
                        discharge_data[0] = discharge_data[1]
                        discharge_data[2] = discharge_data[1]
                        
                    elif channel_idx == 3:
                        battery_data[0] = battery_data[2]
                        #battery_data[1] = battery_data[2]
                        charging_data[0] = charging_data[2]
                        #charging_data[1] = charging_data[2] 
                        discharge_data[0] = discharge_data[2]
                        #discharge_data[1] = discharge_data[2]                 
                        
                     
                    percentages  = self.compute_battery_percentages(battery_data)
                    isPCB = self.check_pcb_status(battery_data, charging_data, discharge_data)
                    
                    print(percentages)
                    
                    battery_publish_data['bp'].extend(percentages)
                    battery_publish_data['pcb_status'].extend(isPCB)
                
                
                try:
                    if mqtt_client != None:
                        mqtt_client.publish('pico/testjig', ujson.dumps(battery_publish_data))
                except Exception as error:
                    print(f"Failed to publish battery and PCB status {error}")
                    mqtt_disconnect()
                    mqtt_client = None

                self.reset()
                self.execute_sequence_step()
                
                print("Time after executing sequence step")
                utime.sleep(2)
                #self.zero_current_count = 0
                
                return True
                        
            return True
        
        for channel_idx in range(3):
            battery_data = charging.monitor(channel_idx + 1, self.resources['i2c1'])      
            print("battery_data channel ", channel_idx + 1,  battery_data)
          
        # Process based on current state
        if self.state == STATE_CHARGING:
            self._update_charging(self.current_sequence, duration)
            print("Update charging", duration)
        elif self.state == STATE_DISCHARGING:
            print("Update discharging", duration)
            self._update_discharging(self.current_sequence, duration)
        elif self.state == STATE_CHARGING_DISCHARGING:
            self._update_charging_discharging(self.current_sequence, duration)
            print("Update charging discharging", duration)
        
       
        print("Test completion Status", self.test_sequence_completion_status)
        
        display_data_json = ujson.dumps(self.display_data)
        
        try:
            if mqtt_client != None:
                mqtt_client.publish('pico/testjig', ujson.dumps(display_data_json))
        except Exception as error:
            print(f"Failed to publish display data {error}")
            mqtt_disconnect()
            mqtt_client = None
        
        # Check if all channels have completed
        if self.test_sequence_completion_status.count(True) == len(self.test_sequence_completion_status):
            
            # Deactivate current channels
            self.deactivate_channels(self.current_sequence)
            
            # Move to next sequence
            self.current_step += 1
            print("current_sequence", self.current_sequence)
            
            # Check if there are more sequences
            if self.current_step < len(self.sequences):
                            
                # Initialize for next sequence
                self.current_sequence = self.sequences[self.current_step]
                
                self.display_data, self.test_sequence_completion_status = self._initialize_display_data(
                    self.current_step, self.current_sequence)
                    
                # Execute next sequence
                completed = self.execute_sequence_step()
                
                if completed:
                    self.state = STATE_COMPLETED
            else:
                self.state = STATE_COMPLETED
                
                try:
                    mqtt_client.publish('pico/testjig', ujson.dumps({"state": self.state}))
                except Exception as error:
                    print(f"Failed to publish state completion status {error}")
                    mqtt_disconnect()
                    mqtt_client = None                                         
                                                
                # Turn on the charging channels so that battery can charge
                print("Turning on the Relay so that battery can be put for charging until the new device is found.", self.isCharging)                
                
                if self.isCharging == False:
                    print("Is charging", self.isCharging)
                    for channel in [1,2,3]:
                        print("333")
                        charging.config(channel=channel, i2c=self.resources['i2c0'], activate=True)
                    self.isCharging = True
                    
            utime.sleep(2)
                
            # Run the gc collect
            gc.collect()     

        return self.state == STATE_COMPLETED
        
        
    def _update_charging(self, current_sequence, duration):
        """
        Update charging status and progress.
        
        Args:
            current_sequence (dict): Current sequence configuration
            duration (int): Elapsed duration in seconds
        """
        try:
            
            # Validate sequence configuration
            if not current_sequence.get("charge", {}).get("required", False):
                return
                
            # Monitor across the required charging channels
            for channel in current_sequence['charge']['channels']:
                # Get display data for this channel
                channel_idx = channel - 1
                
                if channel_idx < 0 or channel_idx >= len(self.display_data):
                    continue
                    
                # Monitor the charging channels
                charging_data = charging.monitor(channel, self.resources['i2c0'])
                
                # The below needs to be removed once the real device is connected                
                if channel == 1:
                    charging_data[1] = charging_data[0]
                    charging_data[2] = charging_data[0] 
                
                elif channel == 2:
                    charging_data[0] = charging_data[1]
                    charging_data[2] = charging_data[1]
                    
                elif channel == 3:
                    charging_data[0] = charging_data[2]
                    #charging_data[1] = charging_data[2]
            
                
                print("charging_data inside update charging", charging_data)
                self.display_data[channel_idx] = charging.update(
                        'charging', charging_data, self.display_data[channel_idx])
                
                # Monitor the battery channels
                #battery_data = charging.monitor(channel, self.resources['i2c1'])
                #self.display_data[channel_idx] = charging.update(
                  #      'battery', battery_data, self.display_data[channel_idx])
                
                # Compute status and progress
                self.display_data[channel_idx] = pico_testjig_mon.compute_charging_status_and_progress(
                    self.display_data[channel_idx], duration)
                
                print("self.display channel idx", self.display_data[channel_idx])
                
                # Check if channel has completed
                if ('progress' in self.display_data[channel_idx] and 
                    self.display_data[channel_idx]['progress'].count(100) == 3):
                    self.test_sequence_completion_status[channel_idx] = True
                    
        except Exception as e:
            print(f"Error updating charging: {e}")
    
            
    def _update_discharging(self, current_sequence, duration):
        """
        Update discharging status and progress.
        
        Args:
            current_sequence (dict): Current sequence configuration
            duration (int): Elapsed duration in seconds
        """
        try:
            # Validate sequence configuration
            if not current_sequence.get("discharge", {}).get("required", False):
                return
                
            # Make sure load_currents exists
            load_currents = current_sequence.get('discharge', {}).get('load_current', [])
            
            # Monitor across the required discharging channels
            for channel_idx in range(len(load_currents)):
                # Skip zero-current channels
                if channel_idx >= len(load_currents) or load_currents[channel_idx] == 0:
                    continue
                    
                if channel_idx >= len(self.display_data):
                    continue
                    
                # Monitor the battery channels
                #battery_data = charging.monitor(channel_idx + 1, self.resources['i2c1'])
                #print("battery_data", battery_data)
                
                #self.display_data[channel_idx] = charging.update(
                #   'battery', battery_data, self.display_data[channel_idx])
                # Monitor the discharge channels
                discharge_data = discharging.monitor(
                    channel_idx + 1, self.resources['uart0'], self.control_mux_list)
                
                # Remove this duplicates when real device is connected
                if channel_idx + 1 == 1:
                    discharge_data[1] = discharge_data[0]
                    discharge_data[2] = discharge_data[0] 
                
                elif channel_idx + 1 == 2:
                    discharge_data[0] = discharge_data[1]
                    discharge_data[2] = discharge_data[1]
                    
                elif channel_idx + 1 == 3:
                    discharge_data[0] = discharge_data[2]
                    #discharge_data[1] = discharge_data[2]
            
                    
                print("discharge data inside discharging", discharge_data)
                self.display_data[channel_idx] = discharging.update(
                    'discharging', discharge_data, self.display_data[channel_idx])
                
                
                # Compute status and progress
                self.display_data[channel_idx] = pico_testjig_mon.compute_discharging_status_and_progress(
                    self.display_data[channel_idx], duration)
                
                # Check if channel has completed
                if ('progress' in self.display_data[channel_idx] and 
                    self.display_data[channel_idx]['progress'].count(0) == 3):
        
                    self.test_sequence_completion_status[channel_idx] = True
        except Exception as e:
            print(f"Error updating discharging: {e}")
    
            
    def _update_charging_discharging(self, current_sequence, duration):
        """
        Update combined charging and discharging status and progress.
        
        Args:
            current_sequence (dict): Current sequence configuration
            duration (int): Elapsed duration in seconds
        """
        try:
            # Validate sequence configuration
            if (not current_sequence.get("charge", {}).get("required", False) or
                not current_sequence.get("discharge", {}).get("required", False)):
                return
                
            # Monitor across the required channels
            for channel in current_sequence['charge']['channels']:
                # Get display data for this channel
                channel_idx = channel - 1
                
                if channel_idx < 0 or channel_idx >= len(self.display_data):
                    continue
                
                # Monitor the charging channels
                charging_data = charging.monitor(channel, self.resources['i2c0'])
                self.display_data[channel_idx] = charging.update(
                    'charging', charging_data, self.display_data[channel_idx])
                
                # Monitor the battery channels
                #battery_data = charging.monitor(channel, self.resources['i2c1'])
                #print("battery_data", battery_data)
                #self.display_data[channel_idx] = charging.update(
                #   'battery', battery_data, self.display_data[channel_idx])
                
                # Monitor the discharging channels
                discharge_data = discharging.monitor(
                    channel, self.resources['uart0'], self.control_mux_list)
                self.display_data[channel_idx] = discharging.update(
                        'discharging', discharge_data, self.display_data[channel_idx])
                
                # Compute status and progress
                self.display_data[channel_idx] = pico_testjig_mon.compute_charging_discharging_status_and_progress(
                    self.display_data[channel_idx], duration)
                
                # Check if channel has completed
                if ('progress' in self.display_data[channel_idx] and 
                    self.display_data[channel_idx]['progress'].count(100) == 3):
                    self.test_sequence_completion_status[channel_idx] = True
                    
        except Exception as e:
            print(f"Error updating charging and discharging: {e}")

# Function to initialize the relay
def initialize_relay(i2c):
    """
    Initialize the relay by setting all ports as outputs.
    
    Args:
        i2c (machine.I2C): I2C interface
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get the device address
        scan_result = i2c.scan()
        if len(scan_result) < 4:
            print(f"Error: Expected at least 4 I2C devices, found {len(scan_result)}")
            return False
            
        relay_address = scan_result[0]
        
        # Configure port0 register as output (0)
        i2c.writeto_mem(relay_address, RELAY_CONFIG_PORT0, bytes([0]))
        utime.sleep_ms(I2C_DELAY_MS)
        
        # Configure port1 register as output (0)
        i2c.writeto_mem(relay_address, RELAY_CONFIG_PORT1, bytes([0]))
        utime.sleep_ms(I2C_DELAY_MS)
        
        return True
    except Exception as e:
        print(f"Error initializing relay: {e}")
        return False
        
# Initialize MUX controls
def initialize_mux_controls():
    """
    Initialize MUX control pins.
    
    Returns:
        list: List of MUX control pin lists
    """
    try:
        # Define pins for each MUX
        control_mux_0 = [machine.Pin(13, machine.Pin.OUT), machine.Pin(14, machine.Pin.OUT), machine.Pin(15, machine.Pin.OUT)]
        control_mux_1 = [machine.Pin(22, machine.Pin.OUT), machine.Pin(21, machine.Pin.OUT), machine.Pin(20, machine.Pin.OUT)]
        control_mux_2 = [machine.Pin(17, machine.Pin.OUT), machine.Pin(19, machine.Pin.OUT), machine.Pin(18, machine.Pin.OUT)]
        
        control_mux_list = [control_mux_0, control_mux_1, control_mux_2]
        
        # Initialize all MUXs to disabled state
        for mux in control_mux_list:
            mux[0].value(MUX_DISABLE)
            
        return control_mux_list
    except Exception as e:
        print(f"Error initializing MUX controls: {e}")


# Function to initialize resources
def initialize_resources():
    """
    Initialize all hardware resources.
    
    Returns:
        dict: Dictionary containing hardware resources
    """
    try:
        # Initialize I2C interfaces
        i2c0 = machine.I2C(1, scl=machine.Pin(3), sda=machine.Pin(2))
        i2c1 = machine.I2C(0, scl=machine.Pin(9), sda=machine.Pin(8))
        
        print(i2c0.scan())
        print(i2c1.scan())
        
        # Initialize UART for electronic load
        electronic_load_uart0 = machine.UART(0, baudrate=9600, tx=machine.Pin(0), rx=machine.Pin(1))
        
        # Initialize relays
        if not initialize_relay(i2c0) or not initialize_relay(i2c1):
            print("Failed to initialize relays")

        
        # Return resources dictionary
        return {
            'i2c0': i2c0,
            'i2c1': i2c1,
            'uart0': electronic_load_uart0
        }
        
    except Exception as e:
        print(f"Error initializing resources: {e}")


# Function to clean up resources
def cleanup_resources(resources):
    """
    Clean up hardware resources.
    
    Args:
        resources (dict): Dictionary of hardware resources
    """
    try:
        # Turn off all charging channels
        if 'i2c0' in resources:
            for channel in range(1, 4):
                charging.config(channel=channel, i2c=resources['i2c0'], activate=False)
        
        """        
        # Turn off all battery channels
        if 'i2c1' in resources:
            for channel in range(1, 4):
                charging.config(channel=channel, i2c=resources['i2c1'], activate=False)
        """        
        # Additional cleanup as needed
        print("Resources cleaned up")
    except Exception as e:
        print(f"Error during cleanup: {e}")


# Function to connect to WiFi network
def wifi_connect(ssid, password):

    # Attempt to connect to the wireless network
    wlan = network.WLAN(network.STA_IF)
    wlan.active(True)

    # Connect with SSID and Password
    wlan.connect(ssid, password)

    # Variable to hold the timeout for the connection
    connection_timeout = 0

    # Wait until the connection is established. Wait for not more than 10 seconds.
    while wlan.isconnected() == False and connection_timeout < 10:

        # Wait for some time
        utime.sleep_ms(500)

        # Increment the timeout
        connection_timeout += 0.5

    # If we are not connected to the network, reset the wlan handle
    if wlan.isconnected() == False:
        
        wlan.disconnect()
        wlan = None

    # Return the wlan handle
    return wlan




# Main function
#@micropython.native
def main():
       
    """Main function with improved error handling and resource management."""
    
    # Global Variables
    global mqtt_client, msg_available, command
    
    # Variable to hold the I2C, UART resources
    resources = None
    
    timer = None
    
    try:
        
        # Defining the host and the port address in the server info json
        #server_info = {'ssid': 'N/A', 'password': 'N/A', 'host': '*******', 'port_info': {'default': 1100}}
        server_info = {'ssid': 'Resonate', 'password': 'internet'}
        
        # wlan is the None
        wlan = None
        
        # Create a timer object
        timer = machine.Timer()           
        
        # Initialize MUX controls
        control_mux_list = initialize_mux_controls()
        
        # Initialize resources
        resources = initialize_resources()
        
        # Create sequencer
        sequencer = TestSequencer(test_sequences, resources, control_mux_list)
        
        # Initialize sequencer
        if not sequencer.initialize():
            print("Error: Failed to initialize sequencer")
            return
            
        
        # Initially the test will be completed
        sequencer.state = STATE_COMPLETED
        
        # Turn on the battery relay and the battery relay. Initally the battery will be in the charged states
        try:
            print("Turning on the battery relay")

            # Activate battery channels
            for channel in range(1, 4):
                charging.config(channel=channel, i2c=resources['i2c0'], activate=True)
                charging.config(channel=channel, i2c=resources['i2c1'], activate=True)
            #print("Battery relay activated successfully")
            
            sequencer.isCharging = True
            
        except Exception as e:
            print(f"Error activating battery relay: {e}")
            
        
        # Define the timer callback
        def timer_callback(timer):
            
            nonlocal sequencer
            sequencer.elapsed_time += 1
        
        # Register the timer - 1 Hz, called once per second
        timer.init(freq=TIMER_FREQUENCY, mode=machine.Timer.PERIODIC, callback=timer_callback)
        
        
        # Main loop
        while True:
            
            if server_info['ssid'] != 'N/A' and wlan == None:

                # Attempt a connection
                wlan = wifi_connect(server_info['ssid'], server_info['password'])
                wifi_retry_count = 0
                
            elif wlan != None and wlan.status() != 3 and wifi_retry_count < 10:

                # Status Reference: https://github.com/georgerobotics/cyw43-driver/blob/9f6405f0b3260968306d782e1c5ac275a46dc65d/src/cyw43.h
                #                 : https://datasheets.raspberrypi.com/picow/connecting-to-the-internet-with-pico-w.pdf
                #
                #     #define CYW43_LINK_DOWN         (0)     ///< link is down
                #     #define CYW43_LINK_JOIN         (1)     ///< Connected to wifi
                #     #define CYW43_LINK_NOIP         (2)     ///< Connected to wifi, but no IP address
                #     #define CYW43_LINK_UP           (3)     ///< Connect to wifi with an IP address
                #     #define CYW43_LINK_FAIL         (-1)    ///< Connection failed
                #     #define CYW43_LINK_NONET        (-2)    ///< No matching SSID found (could be out of range, or down)
                #     #define CYW43_LINK_BADAUTH      (-3)    ///< Authenticatation failure
            #

                # Wait for some time
                wifi_retry_count = wifi_retry_count + 1

            elif wlan != None and wlan.status() != 3 and wifi_retry_count == 10:

                # Disconnect from wlan and reset the wlan
                wlan.disconnect()
                wlan = None

            # Just in case there was a valid mqtt_client, reset it
                if mqtt_client != None:
                    mqtt_disconnect(mqtt_client)
                mqtt_client = None

            elif wlan != None and wlan.status() == 3 and mqtt_client == None:

                # Attempt for mqtt connection.
                mqtt_client = mqtt_connect("*************", 1883, 'resonate', "Resonate@123")
                print("mqtt client connected", mqtt_client)
                
                if mqtt_client != None:
                    mqtt_subscribe(mqtt_client)         

            # Don't proceed further if no mqtt or wifi
            if wlan == None or mqtt_client == None:
                continue 
            
            # Check for the subscribe messages.
            if mqtt_client is not None:
                
                try:
                    mqtt_client.check_msg()
                    
                    if msg_available:
                        
                        print(f"Command:  {command}")
                        
                        # Handle commands based on the message from the web below. Need to check on what commands availble and handle the same.
                        
                        msg_available = False
                              
                    
                except Exception as e:
                    print(f"Error checking MQTT messages: {e}")
                    # Attempt to reconnect if there's an error
                    mqtt_client = None 
            
            
            # Get elapsed time
            duration = sequencer.elapsed_time
            sequencer.elapsed_time = 0 
            
            # Update sequencer
            completed = sequencer.update(duration)
            
            # Optional: add a small delay to prevent busy-waiting
            utime.sleep_ms(MAIN_LOOP_DELAY_MS)
            

    except Exception as e:
        print(f"Main Unexpected error: {e}")
    
    """
    finally:
        
        # Cleanup resources
        try:
            if resources:
                cleanup_resources(resources)
                #pass
                
            # Deinitialize timer if needed
            if timer:
                timer.deinit()
                
        except Exception as e:
            print(f"Error during cleanup: {e}")
    
    
    """    


# Entry point
if __name__ == "__main__":
    main()