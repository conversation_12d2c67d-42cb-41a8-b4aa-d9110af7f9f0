'use client';

import { useEffect, useState } from 'react';
import { Wifi, WifiOff } from 'lucide-react';
import sseService from '@/lib/sse-service';

export function SSEStatus() {
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Register connection handler
    sseService.onConnectionChange(setIsConnected);

    // Cleanup on unmount
    return () => {
      sseService.removeConnectionHandler(setIsConnected);
    };
  }, []);

  return (
    <div
      className={`flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs font-medium ${
        isConnected
          ? 'bg-green-100 text-green-700 border border-green-200'
          : 'bg-red-100 text-red-700 border border-red-200'
      }`}
      title={isConnected ? 'Connected to server' : 'Disconnected from server'}
    >
      {isConnected ? (
        <>
          <Wifi className="h-3 w-3" />
          <span className="hidden sm:inline">Connected</span>
        </>
      ) : (
        <>
          <WifiOff className="h-3 w-3" />
          <span className="hidden sm:inline">Disconnected</span>
        </>
      )}
    </div>
  );
}
