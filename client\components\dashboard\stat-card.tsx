'use client';

import { LucideIcon } from 'lucide-react';

interface StatCardProps {
  icon: LucideIcon;
  label: string;
  value: string | number;
  iconColor?: string;
  valueColor?: string;
}

export function StatCard({
  icon: Icon,
  label,
  value,
  iconColor = 'text-blue-600',
  valueColor,
}: StatCardProps) {
  // Extract color name from the iconColor class (e.g., "blue" from "text-blue-600")
  const colorName = iconColor.split('-')[1];

  // Map color names to gradient backgrounds
  const gradientMap: Record<string, string> = {
    'blue': 'bg-gradient-to-br from-blue-500 to-blue-600',
    'indigo': 'bg-gradient-to-br from-indigo-500 to-indigo-600',
    'cyan': 'bg-gradient-to-br from-cyan-500 to-cyan-600',
    'violet': 'bg-gradient-to-br from-violet-500 to-violet-600',
    'emerald': 'bg-gradient-to-br from-emerald-500 to-emerald-600',
    'teal': 'bg-gradient-to-br from-teal-500 to-teal-600',
    'green': 'bg-gradient-to-br from-green-500 to-green-600',
    'red': 'bg-gradient-to-br from-red-500 to-red-600',
  };

  const gradientBg = gradientMap[colorName] || gradientMap['blue'];

  return (
    <div className="bg-white rounded-lg p-2.5 flex items-center gap-2.5 shadow-md border border-slate-200 h-[68px]">
      <div className={`${gradientBg} p-2 rounded-md shadow-sm`}>
        <Icon className="h-4 w-4 text-white" />
      </div>
      <div className="min-w-0 flex-1">
        <p className="text-xs font-semibold text-slate-500 tracking-wide uppercase truncate">
          {label}
        </p>
        <p className={`text-base font-semibold truncate ${valueColor || 'text-slate-800'} mt-0.5`}>
          {value}
        </p>
      </div>
    </div>
  );
}

export function ResultsStatCard({
  icon: Icon,
  label,
  successValue,
  failureValue,
  iconColor = 'text-blue-600',
}: {
  icon: LucideIcon;
  label: string;
  successValue: number;
  failureValue: number;
  iconColor?: string;
}) {
  // Extract color name from the iconColor class (e.g., "blue" from "text-blue-600")
  const colorName = iconColor.split('-')[1];

  // Map color names to gradient backgrounds
  const gradientMap: Record<string, string> = {
    'blue': 'bg-gradient-to-br from-blue-500 to-blue-600',
    'indigo': 'bg-gradient-to-br from-indigo-500 to-indigo-600',
    'cyan': 'bg-gradient-to-br from-cyan-500 to-cyan-600',
    'violet': 'bg-gradient-to-br from-violet-500 to-violet-600',
    'emerald': 'bg-gradient-to-br from-emerald-500 to-emerald-600',
    'teal': 'bg-gradient-to-br from-teal-500 to-teal-600',
    'green': 'bg-gradient-to-br from-green-500 to-green-600',
    'red': 'bg-gradient-to-br from-red-500 to-red-600',
  };

  const gradientBg = gradientMap[colorName] || gradientMap['blue'];

  return (
    <div className="bg-white rounded-lg p-2.5 flex items-center gap-2.5 shadow-md border border-slate-200 h-[68px]">
      <div className={`${gradientBg} p-2 rounded-md shadow-sm`}>
        <Icon className="h-4 w-4 text-white" />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-semibold text-slate-500 tracking-wide uppercase truncate">
          {label}
        </p>
        <div className="flex items-center gap-3 mt-0.5">
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-green-400 to-green-500 mr-1.5 shadow-sm"></div>
            <p className="text-base font-semibold text-green-600">{successValue}</p>
          </div>
          <div className="h-4 w-px bg-slate-200"></div>
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-red-400 to-red-500 mr-1.5 shadow-sm"></div>
            <p className="text-base font-semibold text-red-600">{failureValue}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
