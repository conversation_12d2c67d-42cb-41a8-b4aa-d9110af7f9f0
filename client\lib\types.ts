// Common type definitions for the battery testing dashboard
import { StatusType, TestStatusType } from './constants';

// Type for device data
export type DeviceData = {
  id: number;
  name: string;
  isCharging: boolean;
  isDischarging: boolean;
  isChargingAndDischarging: boolean; // Property for Sequence 3
  hasState3Occurred: boolean; // Tracks if state 3 occurred during the test
  batteryLevel: number;
  progress: number;
  timeRemaining: number; // minutes
  chargingVoltage: number;
  chargingCurrent: number;
  dischargingVoltage: number;
  dischargingCurrent: number;
  chargingStatus: StatusType | boolean;
  dischargingStatus: StatusType | boolean;
  chargingAndDischargingStatus: StatusType | boolean; // Status for Sequence 3
  chargingStatusMessage: string;
  dischargingStatusMessage: string;
  chargingAndDischargingStatusMessage: string; // Status message for Sequence 3
  testStatus: TestStatusType;
};

// Re-export TestStatus type for backward compatibility
export type TestStatus = TestStatusType;

// Base type for test data
export interface TestDataBase {
  // Success and failure counts for PCBs
  successPcbs: number;
  failurePcbs: number;
}

// Type for test statistics
export type TestStats = {
  duration: number; // in seconds
  panelsTested: number;
  pcbsTested: number;
  successPcbs: number;
  failurePcbs: number;
};

// Type for test results
export type TestResults = {
  [deviceId: number]: {
    chargingSuccess?: boolean;
    dischargingSuccess?: boolean;
    chargingAndDischargingSuccess?: boolean; // Property for Sequence 3
  };
};

// Type for test reports
export interface TestReport extends TestDataBase {
  id: string;
  date: string;
  variant: string;
  serialNumber: string;
}

// SSE Channel Data Types
export interface ChannelData {
  channel: number;
  progress: number[];
  elapsed_time: number[];
  charging?: {
    C: number[];
    V: number[];
  };
  discharging?: {
    C: number[];
    V: number[];
  };
  status: boolean[];
}

// Array of channel data
export type DeviceChannelData = ChannelData[];

// Product variant types
export type ProductVariant = {
  name: string;
  description: string;
  specs: {
    voltage: string;
    capacity: string;
    dimensions: string;
  };
};

export type ProductVariants = {
  [key: string]: ProductVariant;
};

// Battery data for PCB Dashboard
export type BatteryData = {
  chargingStatus: boolean;
  dischargingStatus: boolean;
  chargingAndDischargingStatus: boolean; // New property for Sequence 3
  chargingVoltage: number;
  chargingCurrent: number;
  dischargingVoltage: number;
  dischargingCurrent: number;
  dischargingTime: number;
  chargingTime: number;
  batteryLevel: number;
};
